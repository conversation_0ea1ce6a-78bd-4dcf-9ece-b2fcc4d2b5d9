#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
申请管理系统
用于管理用户的点数申请和反馈消息
"""

import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from beijing_time import beijing_now_iso, beijing_now, format_beijing_time


class ApplicationSystem:
    """申请管理系统"""
    
    def __init__(self, data_file: str = 'applications.json'):
        self.data_file = data_file
        self.applications_data = self.load_applications_data()
    
    def load_applications_data(self) -> Dict:
        """加载申请数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载申请数据失败: {e}")
                return self.create_default_data()
        else:
            return self.create_default_data()
    
    def create_default_data(self) -> Dict:
        """创建默认数据结构"""
        return {
            'applications': [],  # 申请记录
            'statistics': {
                'total_applications': 0,
                'pending_applications': 0,
                'approved_applications': 0,
                'rejected_applications': 0,
                'total_points_requested': 0,
                'total_points_approved': 0
            }
        }
    
    def save_applications_data(self) -> bool:
        """保存申请数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.applications_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError as e:
            print(f"保存申请数据失败: {e}")
            return False
    
    def submit_application(self, username: str, application_type: str, 
                          data: Dict, user_info: Dict = None) -> Tuple[bool, str]:
        """提交申请"""
        try:
            # 生成申请ID
            application_id = len(self.applications_data['applications']) + 1
            
            # 创建申请记录
            application = {
                'id': application_id,
                'username': username,
                'type': application_type,  # 'points' 或 'feedback'
                'status': 'pending',  # pending, approved, rejected
                'submit_time': beijing_now_iso(),
                'process_time': None,
                'processed_by': None,
                'admin_note': None,
                'user_info': {
                    'current_points': user_info.get('points', 0) if user_info else 0,
                    'total_checkins': user_info.get('total_checkins', 0) if user_info else 0,
                    'registration_date': user_info.get('registration_date', '') if user_info else ''
                }
            }
            
            # 根据申请类型添加特定数据
            if application_type == 'points':
                application.update({
                    'points_amount': data.get('points_amount', 0),
                    'reason': data.get('reason', ''),
                })
                # 更新统计
                self.applications_data['statistics']['total_points_requested'] += data.get('points_amount', 0)
            elif application_type == 'feedback':
                application.update({
                    'feedback_type': data.get('feedback_type', ''),
                    'title': data.get('title', ''),
                    'content': data.get('content', ''),
                })
            
            # 添加到申请列表
            self.applications_data['applications'].append(application)
            
            # 更新统计
            self.applications_data['statistics']['total_applications'] += 1
            self.applications_data['statistics']['pending_applications'] += 1
            
            # 保存数据
            if self.save_applications_data():
                if application_type == 'points':
                    return True, f"点数申请提交成功！申请编号：{application_id}，申请 {data.get('points_amount', 0)} 点数，请等待管理员审核。"
                else:
                    return True, f"反馈提交成功！反馈编号：{application_id}，感谢您的宝贵意见。"
            else:
                return False, "申请提交失败，请稍后重试"
                
        except Exception as e:
            print(f"提交申请时发生错误: {e}")
            return False, "申请提交失败，请稍后重试"
    
    def get_applications(self, status: str = None, application_type: str = None, 
                        limit: int = 50, offset: int = 0) -> List[Dict]:
        """获取申请列表"""
        applications = self.applications_data['applications'].copy()
        
        # 过滤条件
        if status:
            applications = [app for app in applications if app['status'] == status]
        
        if application_type:
            applications = [app for app in applications if app['type'] == application_type]
        
        # 按提交时间倒序排列
        applications.sort(key=lambda x: x['submit_time'], reverse=True)
        
        # 分页
        return applications[offset:offset + limit]
    
    def get_user_applications(self, username: str, limit: int = 20) -> List[Dict]:
        """获取用户的申请记录"""
        user_applications = [
            app for app in self.applications_data['applications']
            if app['username'] == username
        ]
        
        # 按提交时间倒序排列
        user_applications.sort(key=lambda x: x['submit_time'], reverse=True)
        return user_applications[:limit]
    
    def get_application_by_id(self, application_id: int) -> Optional[Dict]:
        """根据ID获取申请"""
        for app in self.applications_data['applications']:
            if app['id'] == application_id:
                return app
        return None
    
    def process_application(self, application_id: int, status: str, 
                           admin_username: str, admin_note: str = '') -> Tuple[bool, str]:
        """处理申请（管理员操作）"""
        try:
            application = self.get_application_by_id(application_id)
            if not application:
                return False, "申请不存在"
            
            if application['status'] != 'pending':
                return False, "申请已被处理"
            
            if status not in ['approved', 'rejected']:
                return False, "无效的处理状态"
            
            # 更新申请状态
            old_status = application['status']
            application['status'] = status
            application['process_time'] = beijing_now_iso()
            application['processed_by'] = admin_username
            application['admin_note'] = admin_note
            
            # 更新统计
            stats = self.applications_data['statistics']
            stats['pending_applications'] -= 1
            
            if status == 'approved':
                stats['approved_applications'] += 1
                if application['type'] == 'points':
                    stats['total_points_approved'] += application.get('points_amount', 0)
            else:
                stats['rejected_applications'] += 1
            
            # 保存数据
            if self.save_applications_data():
                return True, f"申请已{('通过' if status == 'approved' else '拒绝')}"
            else:
                # 回滚状态
                application['status'] = old_status
                application['process_time'] = None
                application['processed_by'] = None
                application['admin_note'] = None
                return False, "处理失败，请稍后重试"
                
        except Exception as e:
            print(f"处理申请时发生错误: {e}")
            return False, "处理失败，请稍后重试"
    
    def get_statistics(self) -> Dict:
        """获取申请统计信息"""
        stats = self.applications_data['statistics'].copy()
        
        # 计算实时统计
        applications = self.applications_data['applications']
        
        # 按类型统计
        points_apps = [app for app in applications if app['type'] == 'points']
        feedback_apps = [app for app in applications if app['type'] == 'feedback']
        
        stats.update({
            'points_applications': len(points_apps),
            'feedback_applications': len(feedback_apps),
            'pending_points_applications': len([app for app in points_apps if app['status'] == 'pending']),
            'pending_feedback_applications': len([app for app in feedback_apps if app['status'] == 'pending']),
        })
        
        # 最近7天的申请数量
        from datetime import timedelta
        seven_days_ago = beijing_now() - timedelta(days=7)
        recent_applications = []
        for app in applications:
            try:
                app_time = datetime.fromisoformat(app['submit_time'].replace('Z', '+00:00'))
                # 确保时间比较的一致性
                if app_time.tzinfo is None:
                    app_time = app_time.replace(tzinfo=seven_days_ago.tzinfo)
                if app_time > seven_days_ago:
                    recent_applications.append(app)
            except (ValueError, TypeError):
                continue
        stats['recent_applications'] = len(recent_applications)
        
        return stats
    
    def get_pending_count(self) -> int:
        """获取待处理申请数量"""
        return len([app for app in self.applications_data['applications'] if app['status'] == 'pending'])
    
    def cleanup_old_applications(self, days_to_keep: int = 90) -> Tuple[int, int]:
        """清理旧申请记录"""
        from datetime import timedelta
        
        cutoff_date = beijing_now() - timedelta(days=days_to_keep)
        original_count = len(self.applications_data['applications'])
        
        # 保留最近的申请和未处理的申请
        filtered_applications = []
        for app in self.applications_data['applications']:
            app_date = datetime.fromisoformat(app['submit_time'].replace('Z', '+00:00')).replace(tzinfo=None)
            if app_date > cutoff_date or app['status'] == 'pending':
                filtered_applications.append(app)
        
        self.applications_data['applications'] = filtered_applications
        
        # 重新计算统计
        self.recalculate_statistics()
        
        # 保存数据
        self.save_applications_data()
        
        cleaned_count = original_count - len(filtered_applications)
        return cleaned_count, len(filtered_applications)
    
    def recalculate_statistics(self):
        """重新计算统计信息"""
        applications = self.applications_data['applications']
        
        stats = {
            'total_applications': len(applications),
            'pending_applications': len([app for app in applications if app['status'] == 'pending']),
            'approved_applications': len([app for app in applications if app['status'] == 'approved']),
            'rejected_applications': len([app for app in applications if app['status'] == 'rejected']),
            'total_points_requested': sum(app.get('points_amount', 0) for app in applications if app['type'] == 'points'),
            'total_points_approved': sum(app.get('points_amount', 0) for app in applications if app['type'] == 'points' and app['status'] == 'approved')
        }
        
        self.applications_data['statistics'] = stats


# 测试函数
def test_application_system():
    """测试申请系统"""
    print("=== 申请系统测试 ===")
    
    # 创建测试实例
    app_system = ApplicationSystem('test_applications.json')
    
    # 测试提交点数申请
    success, message = app_system.submit_application(
        username='test_user',
        application_type='points',
        data={
            'points_amount': 100,
            'reason': '用于学习AI绘画'
        },
        user_info={
            'points': 50,
            'total_checkins': 5,
            'registration_date': '2024-01-01'
        }
    )
    print(f"点数申请: {success}, {message}")
    
    # 测试提交反馈
    success, message = app_system.submit_application(
        username='test_user',
        application_type='feedback',
        data={
            'feedback_type': 'feature',
            'title': '建议添加新功能',
            'content': '希望能添加批量生成功能'
        }
    )
    print(f"反馈提交: {success}, {message}")
    
    # 测试获取申请列表
    applications = app_system.get_applications()
    print(f"申请列表数量: {len(applications)}")
    
    # 测试统计信息
    stats = app_system.get_statistics()
    print(f"统计信息: {stats}")
    
    # 清理测试文件
    if os.path.exists('test_applications.json'):
        os.remove('test_applications.json')
    
    print("=== 测试完成 ===")


if __name__ == "__main__":
    test_application_system()
