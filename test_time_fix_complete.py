#!/usr/bin/env python3
"""
测试北京时间修复是否完全有效
"""

from generation_history import GenerationHistoryManager
from beijing_time import beijing_now_iso, beijing_now, format_beijing_time
from datetime import datetime, timezone, timedelta
import json
import os

def test_generation_history_time():
    """测试生成历史记录的时间处理"""
    print("=== 生成历史记录时间处理测试 ===")
    
    # 创建临时的历史管理器
    temp_history_file = "test_history.json"
    history_manager = GenerationHistoryManager(history_file=temp_history_file)
    
    try:
        # 添加一个测试记录
        print("1. 添加测试记录...")
        history_manager.add_generation_record(
            username="test_user",
            generation_type="image",
            prompt="test prompt",
            success=True,
            image_url="http://example.com/test.jpg",
            model_name="Test Model"
        )
        
        # 获取记录并检查时间戳
        records = history_manager.get_user_history("test_user")
        if records:
            record = records[0]
            timestamp_str = record['timestamp']
            print(f"记录时间戳: {timestamp_str}")
            
            # 解析时间戳
            record_time = datetime.fromisoformat(timestamp_str)
            current_beijing_time = beijing_now()
            
            print(f"记录时间: {record_time}")
            print(f"当前北京时间: {current_beijing_time}")
            
            # 计算时间差
            time_diff = abs((current_beijing_time - record_time).total_seconds())
            print(f"时间差: {time_diff:.2f} 秒")
            
            if time_diff < 5:  # 5秒内认为是正确的
                print("✅ 时间戳记录正确")
            else:
                print("❌ 时间戳记录可能有问题")
        
        # 测试画廊记录获取
        print("\n2. 测试画廊记录获取...")
        gallery_records = history_manager.get_recent_gallery_records(hours=1)
        print(f"获取到 {len(gallery_records)} 条画廊记录")
        
        if gallery_records:
            record = gallery_records[0]
            print(f"画廊记录时间戳: {record['timestamp']}")
        
        # 测试用户画廊记录获取
        print("\n3. 测试用户画廊记录获取...")
        user_gallery_records = history_manager.get_user_gallery_records("test_user", hours=1)
        print(f"获取到 {len(user_gallery_records)} 条用户画廊记录")
        
        if user_gallery_records:
            record = user_gallery_records[0]
            print(f"用户画廊记录时间戳: {record['timestamp']}")
        
    finally:
        # 清理测试文件
        if os.path.exists(temp_history_file):
            os.remove(temp_history_file)
        if os.path.exists(temp_history_file + ".backup"):
            os.remove(temp_history_file + ".backup")

def test_timezone_consistency():
    """测试时区一致性"""
    print("\n=== 时区一致性测试 ===")
    
    # 创建不同时区的时间戳
    beijing_tz = timezone(timedelta(hours=8))
    utc_tz = timezone.utc
    
    # 当前北京时间
    beijing_time = datetime.now(beijing_tz)
    print(f"北京时间: {beijing_time}")
    print(f"北京时间ISO: {beijing_time.isoformat()}")
    
    # 转换为UTC时间
    utc_time = beijing_time.astimezone(utc_tz)
    print(f"对应UTC时间: {utc_time}")
    print(f"UTC时间ISO: {utc_time.isoformat()}")
    
    # 测试解析
    beijing_iso = beijing_time.isoformat()
    parsed_time = datetime.fromisoformat(beijing_iso)
    print(f"解析后的时间: {parsed_time}")
    
    # 验证时区转换
    if parsed_time.tzinfo is not None:
        converted_time = parsed_time.astimezone(beijing_tz)
        print(f"转换为北京时间: {converted_time}")
        
        time_diff = abs((beijing_time - converted_time).total_seconds())
        print(f"时间差: {time_diff:.2f} 秒")
        
        if time_diff < 1:
            print("✅ 时区转换正确")
        else:
            print("❌ 时区转换有问题")

def simulate_different_server_timezones():
    """模拟不同服务器时区的情况"""
    print("\n=== 模拟不同服务器时区测试 ===")
    
    # 模拟服务器在不同时区
    timezones = [
        ("北京服务器", 8),
        ("东京服务器", 9),
        ("纽约服务器", -5),
        ("伦敦服务器", 0),
    ]
    
    for server_name, tz_offset in timezones:
        print(f"\n--- {server_name} (UTC{tz_offset:+d}) ---")
        
        # 模拟服务器本地时间
        server_tz = timezone(timedelta(hours=tz_offset))
        server_local_time = datetime.now(server_tz)
        
        print(f"服务器本地时间: {server_local_time}")
        
        # 如果使用错误的方式（本地时间 + 北京时区标识）
        wrong_beijing_time = datetime.now().isoformat() + '+08:00'
        print(f"错误方式生成的时间戳: {wrong_beijing_time}")
        
        # 正确的方式（真正的北京时间）
        correct_beijing_time = beijing_now_iso()
        print(f"正确方式生成的时间戳: {correct_beijing_time}")
        
        # 解析并比较
        wrong_parsed = datetime.fromisoformat(wrong_beijing_time)
        correct_parsed = datetime.fromisoformat(correct_beijing_time)
        
        time_diff = abs((correct_parsed - wrong_parsed).total_seconds())
        print(f"两种方式的时间差: {time_diff:.0f} 秒 = {time_diff/3600:.1f} 小时")

if __name__ == "__main__":
    test_generation_history_time()
    test_timezone_consistency()
    simulate_different_server_timezones()
