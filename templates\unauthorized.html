<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问受限 - 梦羽绘图工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .unauthorized-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            margin: 20px;
        }

        .unauthorized-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 30px;
        }

        .unauthorized-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .unauthorized-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
            border-radius: 50px;
            padding: 15px 30px;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .user-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .user-info h5 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .user-info p {
            margin: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="unauthorized-container">
        <div class="unauthorized-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <h1 class="unauthorized-title">访问受限</h1>
        
        <div class="unauthorized-message">
            {{ message or "您没有权限访问此页面。" }}
        </div>

        {% if current_user %}
        <div class="user-info">
            <h5><i class="fas fa-user me-2"></i>当前用户信息</h5>
            <p><strong>用户名：</strong>{{ current_user.username }}</p>
            <p><strong>积分：</strong>{{ current_user.points }}</p>
            <p><strong>状态：</strong>
                {% if current_user.get('is_admin') %}
                    <span class="badge bg-danger">管理员</span>
                {% elif current_user.get('is_authorized') %}
                    <span class="badge bg-success">授权用户</span>
                {% else %}
                    <span class="badge bg-secondary">普通用户</span>
                {% endif %}
            </p>
        </div>
        {% endif %}

        <div class="mt-4">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>返回首页
            </a>
            
            {% if current_user and not current_user.get('is_authorized') and not current_user.get('is_admin') %}
            <a href="/apply" class="btn btn-outline-primary">
                <i class="fas fa-paper-plane me-2"></i>申请授权
            </a>
            {% endif %}
        </div>

        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                如需帮助，请联系管理员或通过申请页面提交申请。
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
