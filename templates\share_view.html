<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ share_data.title }} - MiaoMiao AI 分享</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .share-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .share-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .share-info {
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .gallery-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .gallery-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-img-container {
            position: relative;
            overflow: hidden;
        }

        .gallery-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .gallery-image:hover {
            transform: scale(1.05);
        }

        .gallery-video {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 5px;
        }

        .type-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 5;
        }

        .card-body {
            padding: 1.5rem;
        }

        .prompt-text {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.9rem;
            line-height: 1.5;
            border-left: 4px solid #667eea;
        }

        .meta-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: #6c757d;
        }

        .user-info, .time-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .model-info {
            background: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            border-radius: 25px;
            padding: 10px 20px;
        }

        .stats-info {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }

        .no-images {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .placeholder-image {
            height: 300px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="btn btn-primary back-btn">
        <i class="fas fa-arrow-left me-1"></i>返回首页
    </a>

    <!-- 分享头部 -->
    <div class="share-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="share-title">
                        <i class="fas fa-share-alt me-2"></i>{{ share_data.title }}
                    </h1>
                    <div class="share-info">
                        <div class="row">
                            <div class="col-md-6">
                                <i class="fas fa-user me-1"></i>分享者：{{ share_data.creator }}
                            </div>
                            <div class="col-md-6">
                                <i class="fas fa-clock me-1"></i>创建时间：{{ share_data.created_at[:19].replace('T', ' ') }}
                            </div>
                        </div>
                        <div class="stats-info mt-3">
                            <div class="row">
                                <div class="col-3">
                                    <i class="fas fa-images me-1"></i>{{ share_data.images|length }} 张图片
                                </div>
                                <div class="col-3">
                                    <i class="fas fa-video me-1"></i>{{ share_data.videos|length if share_data.videos else 0 }} 个视频
                                </div>
                                <div class="col-3">
                                    <i class="fas fa-eye me-1"></i>{{ share_data.view_count }} 次查看
                                </div>
                                <div class="col-3">
                                    <i class="fas fa-calendar me-1"></i>{{ share_data.expire_at[:10] }} 过期
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分享内容 -->
    <div class="container">
        {% set has_content = share_data.images or share_data.videos %}
        {% if has_content %}
            <div class="row" id="galleryGrid">
                <!-- 显示图片 -->
                {% for image in share_data.images %}
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="gallery-card">
                        <div class="card-img-container">
                            <img src="{{ image.image_url }}" class="gallery-image" alt="分享的图片" loading="lazy"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="placeholder-image" style="display: none;">
                                <div class="text-center">
                                    <i class="fas fa-image fa-2x mb-2"></i><br>
                                    图片加载失败
                                </div>
                            </div>
                            <span class="badge bg-primary type-badge">图片</span>
                        </div>
                        <div class="card-body">
                            <div class="meta-info">
                                <span class="user-info">
                                    <i class="fas fa-user"></i>{{ image.username }}
                                </span>
                                <span class="time-info">
                                    <i class="fas fa-clock"></i>{{ image.timestamp[:19].replace('T', ' ') if image.timestamp else '未知时间' }}
                                </span>
                            </div>

                            {% if image.model_name %}
                            <div class="model-info">
                                <i class="fas fa-robot me-1"></i>{{ image.model_name }}
                            </div>
                            {% endif %}

                            {% if image.prompt %}
                            <div class="mb-2">
                                <strong>提示词：</strong>
                                <div class="prompt-text">{{ image.prompt }}</div>
                            </div>
                            {% endif %}

                            <div class="d-grid">
                                <a href="{{ image.image_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>查看原图
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- 显示视频 -->
                {% for video in share_data.videos %}
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="gallery-card">
                        <div class="card-img-container">
                            <video class="gallery-video" controls preload="metadata">
                                <source src="{{ video.video_url }}" type="video/mp4">
                                您的浏览器不支持视频播放。
                            </video>
                            <span class="badge bg-success type-badge">视频</span>
                        </div>
                        <div class="card-body">
                            <div class="meta-info">
                                <span class="user-info">
                                    <i class="fas fa-user"></i>{{ video.username }}
                                </span>
                                <span class="time-info">
                                    <i class="fas fa-clock"></i>{{ video.timestamp[:19].replace('T', ' ') if video.timestamp else '未知时间' }}
                                </span>
                            </div>

                            {% if video.model_name %}
                            <div class="model-info">
                                <i class="fas fa-robot me-1"></i>{{ video.model_name }}
                            </div>
                            {% endif %}

                            {% if video.prompt %}
                            <div class="mb-2">
                                <strong>提示词：</strong>
                                <div class="prompt-text">{{ video.prompt }}</div>
                            </div>
                            {% endif %}

                            <div class="d-grid">
                                <a href="{{ video.video_url }}" target="_blank" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>查看原视频
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-images">
                <i class="fas fa-images fa-3x mb-3 text-muted"></i>
                <h3>暂无内容</h3>
                <p>此分享暂时没有图片或视频内容</p>
            </div>
        {% endif %}
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="预览图片">
                </div>
                <div class="modal-footer">
                    <a id="modalImageLink" href="" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-1"></i>查看原图
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化图片预览功能
        document.querySelectorAll('.gallery-image').forEach(img => {
            img.addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('imageModal'));
                const modalImage = document.getElementById('modalImage');
                const modalLink = document.getElementById('modalImageLink');

                modalImage.src = this.src;
                modalLink.href = this.src;
                modal.show();
            });
        });

        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加进入动画
            const cards = document.querySelectorAll('.gallery-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
