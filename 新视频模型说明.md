# 新增视频模型说明

## 概述
已成功添加新的视频生成模型 "视频生成模型服务器5 wan2.2-5b"，该模型使用新版本的API接口。

## 新模型信息
- **模型名称**: 视频生成模型服务器5 wan2.2-5b
- **服务器地址**: ginigen-wan-2-2-5b.hf.space
- **API版本**: v2 (新版本)
- **API地址**: https://ginigen-wan-2-2-5b.hf.space/gradio_api/call/generate_video

## API参数格式
新版本API的数据格式如下：
```json
{
  "data": [
    {"图片参数": "与之前的视频模型相同"},
    "提示词",
    "704*1280或1280*704",
    "时长默认3",
    "采样步数默认30",
    "Scale默认5",
    "Sample Shift默认5",
    "Seed默认-1随机"
  ]
}
```

## 前端界面更新
1. **新增参数控件**:
   - 分辨率选择器 (704×1280 竖屏 / 1280×704 横屏)
   - 时长输入框 (1-10秒)
   - Scale输入框 (1-20)
   - Sample Shift输入框 (1-20)
   - 专用种子输入框 (-1表示随机)

2. **界面逻辑**:
   - 根据选择的模型自动显示对应版本的参数
   - 新版本API (v2) 显示新参数界面
   - 原版本API (v1) 显示原有参数界面

## 后端代码更新
1. **新增函数**: `generate_video_v2()` - 专门处理新版本API
2. **路由更新**: `/generate_video` 路由现在支持两种API版本
3. **参数处理**: 根据模型的 `api_version` 属性选择相应的处理逻辑

## 配置更新
在 `MODELS` 列表中添加了新模型配置：
```python
{
    "display_name": "视频生成模型服务器5 wan2.2-5b",
    "source_url": "ginigen-wan-2-2-5b.hf.space",
    "proxy_url": "",
    "type": "video",
    "api_version": "v2"  # 标记为新版本API
}
```

## 使用方法
1. 在模型选择器中选择 "视频生成模型服务器5 wan2.2-5b"
2. 上传图片或提供图片URL
3. 输入视频描述提示词
4. 调整新版本API的专用参数：
   - 选择分辨率 (竖屏或横屏)
   - 设置视频时长
   - 调整Scale和Sample Shift参数
   - 设置种子值 (-1为随机)
5. 点击"生成视频"按钮

## 兼容性
- 原有的视频模型 (wan2.1-fast) 继续使用原版本API
- 新模型 (wan2.2-5b) 使用新版本API
- 两种API版本可以并存，系统会自动选择正确的处理方式

## 注意事项
- 新版本API的参数格式与原版本不同
- 新模型支持更多的自定义参数
- 建议在实际使用中测试新模型的效果和稳定性
