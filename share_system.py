import json
import os
import secrets
import uuid
from datetime import datetime, timedelta
from beijing_time import beijing_now_iso, beijing_add_days_iso


class ShareSystem:
    """分享链接管理系统"""
    
    def __init__(self, data_file='data/shares.json'):
        self.data_file = data_file
        self.shares_data = self.load_shares_data()
    
    def load_shares_data(self):
        """加载分享数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认数据结构
                return {
                    'shares': {},  # 分享链接数据
                    'statistics': {
                        'total_shares_created': 0,
                        'total_views': 0,
                        'active_shares': 0
                    }
                }
        except (IOError, json.JSONDecodeError):
            return {
                'shares': {},
                'statistics': {
                    'total_shares_created': 0,
                    'total_views': 0,
                    'active_shares': 0
                }
            }
    
    def save_shares_data(self):
        """保存分享数据"""
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.shares_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def generate_share_id(self, length=12):
        """生成分享ID"""
        # 使用大小写字母和数字，避免容易混淆的字符
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    def generate_access_key(self, length=8):
        """生成访问秘钥"""
        # 使用大写字母和数字，避免容易混淆的字符
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    def create_share(self, creator_username, media_records, expire_days=7, require_key=True, title=""):
        """创建分享链接

        Args:
            creator_username: 创建者用户名
            media_records: 媒体记录列表，每个记录包含图片或视频URL和相关信息
            expire_days: 过期天数，固定为7天
            require_key: 是否需要访问秘钥，固定为True
            title: 分享标题

        Returns:
            tuple: (share_id, access_key) 或 (None, None) 如果失败
        """
        if not media_records:
            return None, None

        # 强制固定设置
        expire_days = 7  # 固定7天过期
        require_key = True  # 固定需要秘钥

        # 生成唯一的分享ID
        share_id = self.generate_share_id()
        while share_id in self.shares_data['shares']:
            share_id = self.generate_share_id()

        # 生成访问秘钥（现在总是需要）
        access_key = self.generate_access_key()

        # 计算过期时间
        expire_time = beijing_add_days_iso(expire_days)

        # 分离图片和视频记录
        images = []
        videos = []
        for record in media_records:
            if record.get('type') == 'video' or record.get('video_url'):
                videos.append(record)
            else:
                images.append(record)

        # 创建分享数据
        share_data = {
            'id': share_id,
            'creator': creator_username,
            'title': title or f"{creator_username}的分享",
            'images': images,  # 图片记录列表
            'videos': videos,  # 视频记录列表
            'access_key': access_key,
            'require_key': require_key,
            'created_at': beijing_now_iso(),
            'expire_at': expire_time,
            'is_active': True,
            'view_count': 0,
            'last_viewed': None
        }

        # 保存分享数据
        self.shares_data['shares'][share_id] = share_data

        # 更新统计
        self.shares_data['statistics']['total_shares_created'] += 1
        self.shares_data['statistics']['active_shares'] += 1

        # 保存到文件
        if self.save_shares_data():
            return share_id, access_key
        else:
            return None, None
    
    def get_share(self, share_id):
        """获取分享信息"""
        return self.shares_data['shares'].get(share_id)
    
    def verify_share_access(self, share_id, access_key=None):
        """验证分享访问权限
        
        Args:
            share_id: 分享ID
            access_key: 访问秘钥（如果需要）
            
        Returns:
            tuple: (success, message, share_data)
        """
        share = self.get_share(share_id)
        
        if not share:
            return False, "分享链接不存在", None
        
        if not share['is_active']:
            return False, "分享链接已失效", None
        
        # 检查是否过期
        try:
            expire_time = datetime.fromisoformat(share['expire_at'].replace('Z', '+00:00'))
            if datetime.now() > expire_time.replace(tzinfo=None):
                return False, "分享链接已过期", None
        except (ValueError, KeyError):
            return False, "分享链接数据异常", None
        
        # 检查访问秘钥
        if share['require_key']:
            if not access_key:
                return False, "需要访问秘钥", None
            if access_key != share['access_key']:
                return False, "访问秘钥错误", None
        
        return True, "验证成功", share
    
    def record_view(self, share_id):
        """记录访问"""
        share = self.get_share(share_id)
        if share:
            share['view_count'] += 1
            share['last_viewed'] = beijing_now_iso()
            self.shares_data['statistics']['total_views'] += 1
            self.save_shares_data()
    
    def get_user_shares(self, username, include_expired=False):
        """获取用户的分享列表"""
        user_shares = []
        current_time = datetime.now()
        
        for share_id, share in self.shares_data['shares'].items():
            if share['creator'] == username:
                # 检查是否过期
                try:
                    expire_time = datetime.fromisoformat(share['expire_at'].replace('Z', '+00:00'))
                    is_expired = current_time > expire_time.replace(tzinfo=None)
                    
                    if include_expired or not is_expired:
                        share_copy = share.copy()
                        share_copy['is_expired'] = is_expired
                        user_shares.append(share_copy)
                except (ValueError, KeyError):
                    continue
        
        # 按创建时间倒序排列
        user_shares.sort(key=lambda x: x['created_at'], reverse=True)
        return user_shares
    
    def delete_share(self, share_id, username=None):
        """删除分享链接
        
        Args:
            share_id: 分享ID
            username: 用户名（如果提供，只能删除自己的分享）
            
        Returns:
            tuple: (success, message)
        """
        share = self.get_share(share_id)
        
        if not share:
            return False, "分享链接不存在"
        
        # 如果指定了用户名，检查权限
        if username and share['creator'] != username:
            return False, "只能删除自己的分享链接"
        
        # 删除分享
        del self.shares_data['shares'][share_id]
        
        # 更新统计
        if share['is_active']:
            self.shares_data['statistics']['active_shares'] -= 1
        
        # 保存数据
        if self.save_shares_data():
            return True, "分享链接已删除"
        else:
            return False, "删除失败，请重试"
    
    def cleanup_expired_shares(self):
        """清理过期的分享链接"""
        current_time = datetime.now()
        expired_shares = []
        
        for share_id, share in self.shares_data['shares'].items():
            try:
                expire_time = datetime.fromisoformat(share['expire_at'].replace('Z', '+00:00'))
                if current_time > expire_time.replace(tzinfo=None):
                    expired_shares.append(share_id)
            except (ValueError, KeyError):
                expired_shares.append(share_id)  # 数据异常的也清理掉
        
        # 删除过期分享
        for share_id in expired_shares:
            if self.shares_data['shares'][share_id]['is_active']:
                self.shares_data['statistics']['active_shares'] -= 1
            del self.shares_data['shares'][share_id]
        
        if expired_shares:
            self.save_shares_data()
        
        return len(expired_shares)
    
    def get_statistics(self):
        """获取统计信息"""
        # 先清理过期分享
        self.cleanup_expired_shares()
        return self.shares_data['statistics'].copy()
