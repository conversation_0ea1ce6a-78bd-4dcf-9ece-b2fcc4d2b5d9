#!/usr/bin/env python3
"""
用户历史记录清理脚本

这个脚本用于定期清理用户的生成历史记录，防止数据文件过大。
可以通过cron job或任务计划程序定期运行。

使用方法:
    python cleanup_history.py [--max-records 50] [--days-to-keep 30] [--dry-run]

参数说明:
    --max-records: 每个用户最大保留记录数 (默认: 50)
    --days-to-keep: 保留天数 (默认: 30)
    --dry-run: 只显示清理统计，不实际执行清理
"""

import argparse
import sys
import os
from datetime import datetime
from auth import UserManager

def main():
    parser = argparse.ArgumentParser(description='清理用户历史记录')
    parser.add_argument('--max-records', type=int, default=50,
                       help='每个用户最大保留记录数 (默认: 50)')
    parser.add_argument('--days-to-keep', type=int, default=30,
                       help='保留天数 (默认: 30)')
    parser.add_argument('--dry-run', action='store_true',
                       help='只显示清理统计，不实际执行清理')
    parser.add_argument('--data-file', default='users.json',
                       help='用户数据文件路径 (默认: users.json)')
    
    args = parser.parse_args()
    
    # 检查数据文件是否存在
    if not os.path.exists(args.data_file):
        print(f"错误: 用户数据文件 '{args.data_file}' 不存在")
        sys.exit(1)
    
    # 创建用户管理器
    user_manager = UserManager(args.data_file)
    
    print(f"开始清理用户历史记录...")
    print(f"配置参数:")
    print(f"  - 最大记录数: {args.max_records}")
    print(f"  - 保留天数: {args.days_to_keep}")
    print(f"  - 数据文件: {args.data_file}")
    print(f"  - 模拟运行: {'是' if args.dry_run else '否'}")
    print("-" * 50)
    
    if args.dry_run:
        # 模拟运行，计算清理统计但不实际清理
        stats = simulate_cleanup(user_manager, args.max_records, args.days_to_keep)
    else:
        # 实际执行清理
        stats = user_manager.cleanup_all_users_history(args.max_records, args.days_to_keep)
    
    # 显示清理结果
    print(f"清理完成!")
    print(f"清理统计:")
    print(f"  - 受影响用户数: {stats['cleaned_users']}")
    print(f"  - 清理前总记录数: {stats['total_records_before']}")
    print(f"  - 清理后总记录数: {stats['total_records_after']}")
    print(f"  - 删除记录数: {stats['records_removed']}")
    
    if stats['records_removed'] > 0:
        reduction_percent = (stats['records_removed'] / stats['total_records_before']) * 100
        print(f"  - 数据减少: {reduction_percent:.1f}%")
    
    if args.dry_run:
        print("\n注意: 这是模拟运行，没有实际修改数据文件。")
    else:
        print(f"\n数据文件已更新: {args.data_file}")

def simulate_cleanup(user_manager, max_records, days_to_keep):
    """模拟清理操作，计算统计信息但不实际修改数据"""
    from datetime import datetime, timedelta
    
    cleaned_users = 0
    total_records_before = 0
    total_records_after = 0
    
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    
    for username, user in user_manager.users.items():
        if 'generation_history' in user:
            records_before = len(user['generation_history'])
            total_records_before += records_before
            
            # 模拟清理过期记录
            filtered_history = []
            for record in user['generation_history']:
                try:
                    record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                    if record_time.replace(tzinfo=None) > cutoff_date:
                        filtered_history.append(record)
                except (ValueError, KeyError):
                    filtered_history.append(record)
            
            # 模拟限制记录数量
            if len(filtered_history) > max_records:
                filtered_history = filtered_history[-max_records:]
            
            records_after = len(filtered_history)
            total_records_after += records_after
            
            if records_before > records_after:
                cleaned_users += 1
    
    return {
        'cleaned_users': cleaned_users,
        'total_records_before': total_records_before,
        'total_records_after': total_records_after,
        'records_removed': total_records_before - total_records_after
    }

if __name__ == '__main__':
    main()
