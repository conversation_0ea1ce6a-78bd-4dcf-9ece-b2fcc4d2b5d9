"""
北京时间工具模块
提供统一的北京时间处理功能，替代直接使用datetime.now()
"""

from datetime import datetime, timedelta, timezone
import time

# 北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))

def beijing_now():
    """
    获取当前北京时间
    
    Returns:
        datetime: 北京时间的datetime对象
    """
    return datetime.now(BEIJING_TZ)

def beijing_now_iso():
    """
    获取当前北京时间的ISO格式字符串
    
    Returns:
        str: 北京时间的ISO格式字符串
    """
    return beijing_now().isoformat()

def beijing_timestamp():
    """
    获取当前北京时间的时间戳
    
    Returns:
        float: 北京时间的时间戳
    """
    return beijing_now().timestamp()

def beijing_from_timestamp(timestamp):
    """
    从时间戳转换为北京时间
    
    Args:
        timestamp (float): 时间戳
        
    Returns:
        datetime: 北京时间的datetime对象
    """
    return datetime.fromtimestamp(timestamp, BEIJING_TZ)

def beijing_from_iso(iso_string):
    """
    从ISO格式字符串转换为北京时间
    如果输入字符串没有时区信息，则假设为北京时间
    
    Args:
        iso_string (str): ISO格式时间字符串
        
    Returns:
        datetime: 北京时间的datetime对象
    """
    dt = datetime.fromisoformat(iso_string)
    
    # 如果没有时区信息，假设为北京时间
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=BEIJING_TZ)
    else:
        # 转换为北京时间
        dt = dt.astimezone(BEIJING_TZ)
    
    return dt

def beijing_add_days(days):
    """
    获取北京时间加上指定天数后的时间
    
    Args:
        days (int): 要添加的天数
        
    Returns:
        datetime: 北京时间的datetime对象
    """
    return beijing_now() + timedelta(days=days)

def beijing_add_days_iso(days):
    """
    获取北京时间加上指定天数后的ISO格式字符串
    
    Args:
        days (int): 要添加的天数
        
    Returns:
        str: 北京时间的ISO格式字符串
    """
    return beijing_add_days(days).isoformat()

def beijing_subtract_days(days):
    """
    获取北京时间减去指定天数后的时间
    
    Args:
        days (int): 要减去的天数
        
    Returns:
        datetime: 北京时间的datetime对象
    """
    return beijing_now() - timedelta(days=days)

def beijing_subtract_days_iso(days):
    """
    获取北京时间减去指定天数后的ISO格式字符串
    
    Args:
        days (int): 要减去的天数
        
    Returns:
        str: 北京时间的ISO格式字符串
    """
    return beijing_subtract_days(days).isoformat()

def is_beijing_time_after(time1, time2):
    """
    比较两个时间，判断time1是否在time2之后
    
    Args:
        time1: 时间1 (datetime对象或ISO字符串)
        time2: 时间2 (datetime对象或ISO字符串)
        
    Returns:
        bool: time1是否在time2之后
    """
    if isinstance(time1, str):
        time1 = beijing_from_iso(time1)
    if isinstance(time2, str):
        time2 = beijing_from_iso(time2)
    
    # 确保都是北京时间
    if time1.tzinfo is None:
        time1 = time1.replace(tzinfo=BEIJING_TZ)
    else:
        time1 = time1.astimezone(BEIJING_TZ)
        
    if time2.tzinfo is None:
        time2 = time2.replace(tzinfo=BEIJING_TZ)
    else:
        time2 = time2.astimezone(BEIJING_TZ)
    
    return time1 > time2

def beijing_time_diff_seconds(time1, time2):
    """
    计算两个时间的差值（秒）
    
    Args:
        time1: 时间1 (datetime对象或ISO字符串)
        time2: 时间2 (datetime对象或ISO字符串)
        
    Returns:
        float: 时间差值（秒），time1 - time2
    """
    if isinstance(time1, str):
        time1 = beijing_from_iso(time1)
    if isinstance(time2, str):
        time2 = beijing_from_iso(time2)
    
    # 确保都是北京时间
    if time1.tzinfo is None:
        time1 = time1.replace(tzinfo=BEIJING_TZ)
    else:
        time1 = time1.astimezone(BEIJING_TZ)
        
    if time2.tzinfo is None:
        time2 = time2.replace(tzinfo=BEIJING_TZ)
    else:
        time2 = time2.astimezone(BEIJING_TZ)
    
    return (time1 - time2).total_seconds()

def format_beijing_time(dt=None, format_str="%Y-%m-%d %H:%M:%S"):
    """
    格式化北京时间为指定格式的字符串
    
    Args:
        dt: datetime对象，如果为None则使用当前北京时间
        format_str: 格式字符串，默认为 "%Y-%m-%d %H:%M:%S"
        
    Returns:
        str: 格式化后的时间字符串
    """
    if dt is None:
        dt = beijing_now()
    elif isinstance(dt, str):
        dt = beijing_from_iso(dt)
    
    # 确保是北京时间
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=BEIJING_TZ)
    else:
        dt = dt.astimezone(BEIJING_TZ)
    
    return dt.strftime(format_str)

# 兼容性函数，用于替换time.time()
def beijing_time_timestamp():
    """
    获取当前北京时间的时间戳，用于替换time.time()
    
    Returns:
        float: 北京时间的时间戳
    """
    return beijing_timestamp()

def test_beijing_time():
    """
    测试北京时间工具函数
    """
    print("=== 北京时间工具测试 ===")
    
    # 测试基本功能
    now = beijing_now()
    print(f"当前北京时间: {now}")
    print(f"ISO格式: {beijing_now_iso()}")
    print(f"时间戳: {beijing_timestamp()}")
    print(f"格式化时间: {format_beijing_time()}")
    
    # 测试时间计算
    future = beijing_add_days(7)
    past = beijing_subtract_days(7)
    print(f"7天后: {future}")
    print(f"7天前: {past}")
    
    # 测试时间比较
    print(f"未来时间是否在过去时间之后: {is_beijing_time_after(future, past)}")
    print(f"时间差值: {beijing_time_diff_seconds(future, past)} 秒")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_beijing_time()
