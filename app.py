import os
import requests
import json
import time
import random
import threading
import base64
import re
import secrets
import urllib.parse
from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for
from PIL import Image
from io import BytesIO
import logging
from datetime import datetime, timedelta

# 导入用户管理和积分系统
from auth import UserManager, login_required, admin_required, points_required
from points_system import PointsSystem
from redemption_system import RedemptionSystem
from application_system import ApplicationSystem
from message_system import MessageSystem
from share_system import ShareSystem
from generation_history import GenerationHistoryManager

# 导入北京时间工具
from beijing_time import beijing_timestamp, beijing_time_timestamp, format_beijing_time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'miaomiao_harem_secret_key'
app.config['UPLOAD_FOLDER'] = 'static/images'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 修改为50 MB 限制

# 获取网站域名的函数
def get_site_domain():
    """获取网站域名，优先从设置中获取，其次从环境变量，最后使用默认值"""
    settings = points_system.get_settings()
    return settings.get('site_domain', os.environ.get('SITE_DOMAIN', 'https://sd.exacg.cc'))

# Linux Do OAuth2 配置
LINUX_DO_CLIENT_ID = 'GZfEhWWjowkChfAn5xtD6SFNfxR9YUt9'
LINUX_DO_CLIENT_SECRET = 'e5FiHlz6YwBdEJOw1VtbOYpAt7TTr4J8'
LINUX_DO_REDIRECT_URI = 'https://sd.exacg.cc/linux'
LINUX_DO_AUTH_URL = 'https://connect.linux.do/oauth2/authorize'
LINUX_DO_TOKEN_URL = 'https://connect.linux.do/oauth2/token'
LINUX_DO_USER_INFO_URL = 'https://connect.linux.do/api/user'

# 添加CSP头部以允许内联脚本和样式
@app.after_request
def add_security_headers(response):
    # 设置Content Security Policy，允许内联脚本和样式
    response.headers['Content-Security-Policy'] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "img-src 'self' data: https: http:; "
        "font-src 'self' https://cdnjs.cloudflare.com; "
        "connect-src 'self' https: http:; "
        "media-src 'self' data: https: http:;"
    )
    return response

# 初始化用户管理和积分系统
user_manager = UserManager()
points_system = PointsSystem()
redemption_system = RedemptionSystem()
application_system = ApplicationSystem()
message_system = MessageSystem()
share_system = ShareSystem()
generation_history_manager = GenerationHistoryManager()

# Linux Do OAuth2 辅助函数
def generate_oauth_state():
    """生成OAuth2状态参数，用于防止CSRF攻击"""
    return secrets.token_urlsafe(32)

def get_linux_do_auth_url():
    """生成Linux Do授权链接"""
    state = generate_oauth_state()
    session['oauth_state'] = state

    params = {
        'client_id': LINUX_DO_CLIENT_ID,
        'redirect_uri': LINUX_DO_REDIRECT_URI,
        'response_type': 'code',
        'scope': 'user',
        'state': state
    }

    auth_url = f'{LINUX_DO_AUTH_URL}?{urllib.parse.urlencode(params)}'
    return auth_url

def get_linux_do_access_token(code):
    """使用授权码获取访问令牌"""
    data = {
        'client_id': LINUX_DO_CLIENT_ID,
        'client_secret': LINUX_DO_CLIENT_SECRET,
        'code': code,
        'redirect_uri': LINUX_DO_REDIRECT_URI,
        'grant_type': 'authorization_code'
    }

    try:
        logger.info(f"正在获取访问令牌，授权码: {code[:10]}...")
        response = requests.post(LINUX_DO_TOKEN_URL, data=data, timeout=30)
        logger.info(f"Token请求响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"Token请求失败，响应内容: {response.text}")

        response.raise_for_status()
        token_data = response.json()
        logger.info(f"成功获取访问令牌，类型: {token_data.get('token_type', 'unknown')}")
        return token_data
    except requests.RequestException as e:
        logger.error(f"获取Linux Do访问令牌失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"响应内容: {e.response.text}")
        return None

def get_linux_do_user_info(access_token):
    """使用访问令牌获取用户信息"""
    headers = {
        'Authorization': f'Bearer {access_token}',
        'User-Agent': 'MiaoMiao-AI-App/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    try:
        logger.info(f"正在获取用户信息，访问令牌: {access_token[:20]}...")
        response = requests.get(LINUX_DO_USER_INFO_URL, headers=headers, timeout=30)
        logger.info(f"用户信息请求响应状态码: {response.status_code}")

        if response.status_code != 200:
            logger.error(f"用户信息请求失败，响应内容: {response.text}")
            logger.error(f"请求头: {headers}")

        response.raise_for_status()
        user_data = response.json()
        logger.info(f"成功获取用户信息，用户ID: {user_data.get('id', 'unknown')}")
        return user_data
    except requests.RequestException as e:
        logger.error(f"获取Linux Do用户信息失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"响应状态码: {e.response.status_code}")
            logger.error(f"响应内容: {e.response.text}")
            logger.error(f"响应头: {dict(e.response.headers)}")
        return None

# 日志缓存
proxy_logs = []

# 视频生成队列管理 - 每个视频模型独立队列
video_queues = {}  # 存储每个视频模型的队列状态
video_queue_locks = {}  # 每个视频模型的独立锁

# 初始化视频模型队列
def init_video_queues():
    """初始化所有视频模型的队列状态"""
    for i, model in enumerate(MODELS):
        if model['type'] == 'video':
            model_key = f"video_model_{i}"
            video_queues[model_key] = {
                'is_processing': False,
                'start_time': None,
                'current_user_info': None,
                'model_name': model['display_name'],
                'model_url': model['source_url']
            }
            video_queue_locks[model_key] = threading.Lock()

# 确保上传文件夹存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 如果需要调试视频生成问题，可以临时启用DEBUG级别
# logging.getLogger().setLevel(logging.DEBUG)

# 全局代理池API地址配置 - 由管理员在后台设置
def get_global_proxy_api_url():
    """获取全局代理池API地址"""
    settings = points_system.get_settings()
    return settings.get('global_proxy_api_url', '')

# 调整分辨率以符合视频生成的要求
def adjust_resolution(width, height):
    """
    调整分辨率，满足以下条件：
    1. 保持原始宽高比
    2. 单边最大值不能超过896
    3. 宽度和高度都必须是32的倍数
    """
    # 计算宽高比
    aspect_ratio = width / height

    # 如果宽度或高度超过896，按比例缩小
    if width > 896 or height > 896:
        if width > height:
            # 宽边是最大的，将其限制在896
            new_width = 896
            new_height = int(new_width / aspect_ratio)
        else:
            # 高边是最大的，将其限制在896
            new_height = 896
            new_width = int(new_height * aspect_ratio)
    else:
        # 如果都没超过限制，保持原始尺寸
        new_width = width
        new_height = height

    # 确保宽度和高度都是32的倍数
    new_width = round(new_width / 32) * 32
    new_height = round(new_height / 32) * 32

    # 确保没有零值（极端情况处理）
    if new_width == 0:
        new_width = 32
    if new_height == 0:
        new_height = 32

    return new_width, new_height

# 定义可用模型列表
MODELS = [
    {
        "display_name": "Miaomiao Harem vPred Dogma 1.1",
        "source_url": "menyu-miaomiaoharemdogma11.hf.space",
        "proxy_url": "",
        "type": "image"
    },{
        "display_name": "Miaomiao Harem vPred Dogma 1.0",
        "source_url": "menyu-miaomiaoharemdogma.hf.space",
        "proxy_url": "miaomaio.menyu.workers.dev",
        "type": "image"
    },
    {
        "display_name": "MiaoMiao Harem 1.6G",
        "source_url": "menyu-miaoharem.hf.space",
        "proxy_url": "mmh.vwo50.workers.dev",
        "type": "image"
    },
    {
        "display_name": "MiaoMiao Pixel 像素 1.0",
        "source_url": "menyu-miaopixelnew.hf.space",
        "proxy_url": "",
        "type": "image"
    },
    {
        "display_name": "NoobAIXL VPred 0.65s",
        "source_url": "menyu-noobvpre065s.hf.space",
        "proxy_url": "",
        "type": "image"
    },
    {
        "display_name": "NoobAIXL V1.1",
        "source_url": "menyu-noobxl11.hf.space",
        "proxy_url": "",
        "type": "image"
    },
    {
        "display_name": "illustrious_pencil 融合",
        "source_url": "menyu-illustrious-pencil-xl.hf.space",
        "proxy_url": "",
        "type": "image"
    },
    {
        "display_name": "Wainsfw illustrious v1.4",
        "source_url": "menyu-wainsfw.hf.space",
        "proxy_url": "",
        "type": "image"
    },
    {
        "display_name": "视频生成模型主要服务器 wan2.1-fast",
        "source_url": "menyu-wan2-1-fast.hf.space",
        "proxy_url": "",
        "type": "video"
    },
    {
        "display_name": "视频生成模型服务器2 wan2.1-fast",
        "source_url": "alekseycalvin-wan2-1-fast-720p-soonr.hf.space",
        "proxy_url": "",
        "type": "video"
    },
    {
        "display_name": "视频生成模型服务器3 wan2.1-fast",
        "source_url": "smiley0707-wan2-1-fast.hf.space",
        "proxy_url": "",
        "type": "video"
    },
    {
        "display_name": "视频生成模型服务器4 wan2.1-fast",
        "source_url": "greff3-wan2-1-fast-video.hf.space",
        "proxy_url": "",
        "type": "video"
    }
]

# 解析和格式化代理地址
def format_proxy_url(proxy_str):
    """
    解析代理字符串并格式化为标准URL格式
    支持以下格式:
    1. IP:端口
    2. 账号:密码@IP:端口
    """
    if not proxy_str:
        return None

    # 已经是完整格式的情况
    if proxy_str.startswith('http://') or proxy_str.startswith('https://'):
        return proxy_str

    # 构建标准格式
    if '@' in proxy_str:
        # 账号:密码@代理地址:端口
        return f"http://{proxy_str}"
    else:
        # 仅IP:端口
        return f"http://{proxy_str}"

# 获取代理IP
def get_proxy_ip(proxy_api_url=None):
    # 如果没有提供API URL，则使用全局设置的代理池API地址
    api_url = proxy_api_url if proxy_api_url else get_global_proxy_api_url()

    # 检查API URL是否有效
    if not api_url:
        logger.warning("代理池API地址未配置，将使用直接连接")
        return None, "代理池API地址未配置，将使用直接连接"

    try:
        logger.info(f"正在从代理池获取代理IP: {api_url}")
        response = requests.get(api_url, timeout=30)
        if response.status_code == 200:
            proxy_text = response.text.strip()
            logger.info(f"代理池响应: {proxy_text}")

            if proxy_text and not proxy_text.startswith("error"):
                # 检查是否有多行代理IP
                proxy_lines = [line.strip() for line in proxy_text.splitlines() if line.strip()]
                if proxy_lines:
                    # 验证代理IP格式 (应该是 IP:端口 格式)
                    valid_proxies = []
                    for line in proxy_lines:
                        # 基本格式验证：应该包含冒号，且格式类似 IP:端口
                        if ':' in line and len(line.split(':')) == 2:
                            ip_part, port_part = line.split(':')
                            # 简单验证端口是数字
                            try:
                                port = int(port_part)
                                if 1 <= port <= 65535:
                                    valid_proxies.append(line)
                            except ValueError:
                                logger.warning(f"无效的代理格式: {line}")

                    if valid_proxies:
                        # 如果有多个有效代理，随机选择一个
                        if len(valid_proxies) > 1:
                            proxy_ip = random.choice(valid_proxies)
                            logger.info(f"从{len(valid_proxies)}个有效代理中随机选择: {proxy_ip}")
                            return proxy_ip, f"从{len(valid_proxies)}个代理中随机选择一个"
                        else:
                            proxy_ip = valid_proxies[0]
                            logger.info(f"获取到代理IP: {proxy_ip}")
                            return proxy_ip, "获取到一个代理IP"
                    else:
                        logger.warning("代理池返回的代理IP格式无效")
                        return None, "代理池返回的代理IP格式无效"
                else:
                    logger.warning("获取到的代理IP列表为空")
                    return None, "获取到的代理IP列表为空"
            else:
                logger.error(f"代理池返回错误: {proxy_text}")
                return None, f"获取代理IP失败: {proxy_text}"
        else:
            logger.error(f"代理池API返回状态码: {response.status_code}")
            return None, f"获取代理IP失败，状态码: {response.status_code}"
    except requests.exceptions.Timeout:
        logger.error("代理池API请求超时")
        return None, "代理池API请求超时，将使用直接连接"
    except requests.exceptions.ConnectionError as e:
        logger.error(f"代理池API连接失败: {e}")
        return None, "代理池API连接失败，将使用直接连接"
    except Exception as e:
        logger.error(f"获取代理IP时发生未知错误: {e}")
        return None, "代理服务连接失败，将使用直接连接"







# 加载随机提示词
def load_random_tag():
    try:
        # 定义可能的文件路径列表
        possible_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "tag.txt"),
            os.path.join(os.getcwd(), "tag.txt")
        ]

        # 找到第一个存在的文件路径
        tag_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                tag_file_path = path
                break

        if not tag_file_path:
            logger.error("错误：词库文件不存在，请确保程序目录下有tag.txt文件")
            return "未找到词库文件"

        # 读取词库文件
        with open(tag_file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        if not lines:
            logger.error("错误：词库文件为空")
            return "词库文件为空"

        # 随机选择一行
        random_tag = random.choice([line.strip() for line in lines if line.strip()])
        return random_tag
    except Exception as e:
        logger.error(f"导入随机提示词时发生错误: {e}")
        return "加载随机提示词失败"

# 生成图像函数
def generate_image(prompt, negative_prompt, width, height, steps, cfg, model_index, seed,
                  enable_hr, restore_faces):
    max_retries = 3
    retry_count = 0

    # 清空日志列表
    global proxy_logs
    proxy_logs = []

    while retry_count < max_retries:
        try:
            # 统一使用管理员配置的代理池
            proxies = None
            proxy_log = ""
            proxy_ip, proxy_msg = get_proxy_ip()
            if proxy_ip:
                # 处理代理格式
                proxy_url = format_proxy_url(proxy_ip)
                proxies = {
                    "http": proxy_url,
                    "https": proxy_url
                }

                # 使用全局代理池，不显示详细代理信息
                proxy_log = "正在连接服务器..."
                logger.info("[全局代理池]使用管理员配置的代理池...")
                proxy_logs.append(proxy_log)
            else:
                proxy_log = "正在连接服务器..."
                logger.info(f"{proxy_msg}")
                logger.info("代理池暂不可用，将使用直接连接")
                proxy_logs.append(proxy_log)

            # 获取当前选择的模型
            selected_model = MODELS[model_index]

            # 统一使用源地址配合代理池
            url = f"https://{selected_model['source_url']}/gradio_api/call/infer"
            logger.info(f"使用源地址：{selected_model['source_url']}")
            logger.info("本次请求使用源地址配合代理池")

            headers = {"Content-Type": "application/json"}
            data = {
                "data": [
                    prompt,
                    negative_prompt,
                    enable_hr,
                    seed,
                    width,
                    height,
                    cfg,
                    steps,
                    restore_faces
                ]
            }

            logger.info("正在发送请求...")
            response = requests.post(url, headers=headers, data=json.dumps(data), proxies=proxies, timeout=60)
            response_json = response.json()
            event_id = response_json.get("event_id")
            logger.info(f"获取到事件ID: {event_id}")

            if not event_id:
                logger.error("获取EVENT_ID失败！")
                proxy_logs.append("连接服务器失败")
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"正在进行第{retry_count+1}次重试...")
                    proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                    continue
                return None, "连接服务器失败，请稍后重试"

            # 第二步：使用EVENT_ID获取结果
            result_url = f"https://{selected_model['source_url']}/gradio_api/call/infer/{event_id}"

            # 轮询等待结果
            logger.info("等待生成结果...")
            poll_count = 0
            while True:
                result_response = requests.get(result_url)
                result_text = result_response.text

                # 定期更新状态
                poll_count += 1
                if poll_count % 5 == 0:
                    logger.info(f"仍在等待生成结果...({poll_count}秒)")

                # 处理SSE格式的响应
                if "event: complete" in result_text:
                    logger.info("生成完成")
                    # 提取complete事件的data部分
                    complete_start = result_text.find("event: complete")
                    if complete_start != -1:
                        # 从complete事件开始查找data
                        data_start = result_text.find("data: ", complete_start)
                        if data_start != -1:
                            data_json_str = result_text[data_start + 6:].strip()
                            # 如果有后续event行，只取到下一个event之前
                            next_event = data_json_str.find("\nevent:")
                            if next_event != -1:
                                data_json_str = data_json_str[:next_event].strip()

                            try:
                                # 解析JSON数组
                                result_data = json.loads(data_json_str)
                                if result_data and len(result_data) >= 2:
                                    # 提取第一个对象中的url字段
                                    image_data = result_data[0]
                                    if isinstance(image_data, dict) and "url" in image_data:
                                        image_url = image_data["url"]
                                        image_id = result_data[1]

                                        logger.info(f"图片URL: {image_url}")
                                        logger.info(f"图片ID: {image_id}")

                                        # 不再下载图片到本地，直接返回云端URL
                                        return {
                                            "image_url": image_url,
                                            "image_id": image_id
                                        }, "成功生成图片"
                                    else:
                                        proxy_logs.append("图片生成失败，请重试")
                                        return None, "图片生成失败，请重试"
                                else:
                                    proxy_logs.append("图片生成失败，请重试")
                                    return None, "图片生成失败，请重试"
                            except json.JSONDecodeError as e:
                                logger.error(f"解析JSON数据失败: {e}")
                                proxy_logs.append("图片生成失败，请重试")
                                return None, "图片生成失败，请重试"
                        else:
                            proxy_logs.append("图片生成失败，请重试")
                            return None, "图片生成失败，请重试"
                    else:
                        proxy_logs.append("图片生成失败，请重试")
                        return None, "图片生成失败，请重试"
                elif "event: heartbeat" in result_text:
                    # 心跳事件，正常情况，继续等待
                    logger.debug("收到heartbeat事件，继续等待...")
                    # 不需要添加到proxy_logs，避免日志过多
                elif "event: error" in result_text:
                    logger.error("生成过程出错！")
                    proxy_logs.append("生成过程出现错误")
                    # 提取error事件的data部分
                    error_start = result_text.find("event: error")
                    if error_start != -1:
                        data_start = result_text.find("data: ", error_start)
                        if data_start != -1:
                            error_data = result_text[data_start + 6:].strip()
                            # 如果有后续event行，只取到下一个event之前
                            next_event = error_data.find("\nevent:")
                            if next_event != -1:
                                error_data = error_data[:next_event].strip()
                            logger.error(f"错误信息: {error_data}")

                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info(f"正在进行第{retry_count+1}次重试...")
                        proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                        break  # 跳出内层循环以重试
                    else:
                        proxy_logs.append("多次重试失败，请稍后再试")
                        return None, "多次重试失败，请稍后再试"
                elif "event:" in result_text:
                    # 其他类型的事件（如estimation, process_starts, progress等），继续等待
                    logger.debug("收到其他SSE事件，继续等待...")
                    # 不需要添加到proxy_logs，避免日志过多
                else:
                    # 没有识别到任何事件，可能是空响应或其他格式，继续等待
                    logger.debug("未识别到SSE事件格式，继续等待...")

                # 等待一小段时间再次查询
                time.sleep(1)

        except Exception as e:
            logger.error(f"请求过程中发生错误: {e}")
            proxy_logs.append("生成过程出现错误")
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"正在进行第{retry_count+1}次重试...")
                proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                continue
            return None, "多次重试失败，请稍后再试"

    return None, "生成图片失败"

# 生成视频函数
def generate_video(image_path_or_data, prompt, width, height, negative_prompt, motion_bucket_id,
                  cond_aug, steps, seed, model_index=None):
    max_retries = 3
    retry_count = 0

    # 获取视频模型
    if model_index is not None:
        video_model = MODELS[model_index]
        model_key = f"video_model_{model_index}"
    else:
        # 如果没有指定模型，使用第一个视频模型
        video_model = next((model for model in MODELS if model['type'] == 'video'), MODELS[-1])
        model_index = next((i for i, model in enumerate(MODELS) if model['type'] == 'video'), len(MODELS)-1)
        model_key = f"video_model_{model_index}"

    # 确保队列已初始化
    if model_key not in video_queues:
        video_queues[model_key] = {
            'is_processing': False,
            'start_time': None,
            'current_user_info': None,
            'model_name': video_model['display_name'],
            'model_url': video_model['source_url']
        }
        video_queue_locks[model_key] = threading.Lock()

    # 设置队列状态为正在处理
    with video_queue_locks[model_key]:
        video_queues[model_key]['is_processing'] = True
        video_queues[model_key]['start_time'] = beijing_time_timestamp()
        video_queues[model_key]['current_user_info'] = f"视频生成任务开始于 {format_beijing_time(format_str='%H:%M:%S')}"

    # 清空日志列表
    global proxy_logs
    proxy_logs = []

    try:
        # 处理图像数据
        image_data = None
        if image_path_or_data.startswith('data:image'):
            # 处理base64编码的图像
            image_format = re.search(r'data:image/(\w+);base64,', image_path_or_data)
            if image_format:
                image_format = image_format.group(1)
                # 提取base64部分
                base64_data = re.sub(r'data:image/\w+;base64,', '', image_path_or_data)
                image_data = {
                    "path": image_path_or_data,
                    "meta": {
                        "_type": "gradio.FileData"
                    }
                }
                logger.info("使用base64编码的图像数据")
                proxy_logs.append("正在处理上传的图像...")

                # 获取原始图像的宽高比
                try:
                    # 解码base64数据获取图像尺寸
                    img_data = base64.b64decode(base64_data)
                    img = Image.open(BytesIO(img_data))
                    orig_width, orig_height = img.size
                    logger.info(f"原始图像尺寸: {orig_width}x{orig_height}")

                    # 调整宽高，保持比例，确保单边最大896，且能被32整除
                    width, height = adjust_resolution(orig_width, orig_height)
                    logger.info(f"调整后的视频分辨率: {width}x{height}")
                    proxy_logs.append(f"图像分辨率已优化为: {width}x{height}")
                except Exception as e:
                    logger.error(f"无法获取图像尺寸: {e}")
                    proxy_logs.append("图像处理出现问题，将使用默认设置")
        else:
            # 使用URL
            image_data = {
                "path": image_path_or_data,
                "meta": {
                    "_type": "gradio.FileData"
                }
            }
            logger.info(f"使用图像URL: {image_path_or_data}")
            proxy_logs.append("正在处理图像链接...")

            # 尝试从URL获取图像尺寸
            try:
                response = requests.get(image_path_or_data, stream=True)
                img = Image.open(BytesIO(response.content))
                orig_width, orig_height = img.size
                logger.info(f"原始图像尺寸: {orig_width}x{orig_height}")

                # 调整宽高，保持比例，确保单边最大896，且能被32整除
                width, height = adjust_resolution(orig_width, orig_height)
                logger.info(f"调整后的视频分辨率: {width}x{height}")
                proxy_logs.append(f"图像分辨率已优化为: {width}x{height}")
            except Exception as e:
                logger.error(f"无法从URL获取图像尺寸: {e}")
                proxy_logs.append("图像处理出现问题，将使用默认设置")

        while retry_count < max_retries:
            try:
                # 统一使用管理员配置的代理池
                proxies = None
                proxy_log = ""
                proxy_ip, proxy_msg = get_proxy_ip()
                if proxy_ip:
                    # 处理代理格式
                    proxy_url = format_proxy_url(proxy_ip)
                    proxies = {
                        "http": proxy_url,
                        "https": proxy_url
                    }

                    # 使用全局代理池，不显示详细代理信息
                    proxy_log = "正在连接视频生成服务器..."
                    logger.info("[全局代理池]使用管理员配置的代理池...")
                    proxy_logs.append(proxy_log)
                else:
                    proxy_log = "正在连接视频生成服务器..."
                    logger.info(f"{proxy_msg}")
                    logger.info("代理池暂不可用，将使用直接连接")
                    proxy_logs.append(proxy_log)

                # 使用已经在函数开头获取的正确视频模型
                # video_model 变量已经在函数开头根据 model_index 正确设置

                # 统一使用源地址配合代理池
                url = f"https://{video_model['source_url']}/gradio_api/call/generate_video"
                logger.info(f"使用源地址：{video_model['source_url']}")
                logger.info("本次请求使用源地址配合代理池")

                headers = {"Content-Type": "application/json"}
                data = {
                    "data": [
                        image_data,
                        prompt,
                        height,
                        width,
                        negative_prompt,
                        3,
                        cond_aug,
                        steps,
                        seed,
                        True
                    ]
                }

                logger.info("正在发送视频生成请求...")
                response = requests.post(url, headers=headers, data=json.dumps(data), proxies=proxies, timeout=60)
                response_json = response.json()
                event_id = response_json.get("event_id")
                logger.info(f"获取到事件ID: {event_id}")

                if not event_id:
                    logger.error("获取EVENT_ID失败！")
                    proxy_logs.append("连接服务器失败")
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info(f"正在进行第{retry_count+1}次重试...")
                        proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                        continue
                    return None, "连接服务器失败，请稍后重试"

                # 第二步：使用EVENT_ID获取结果
                result_url = f"https://{video_model['source_url']}/gradio_api/call/generate_video/{event_id}"

                # 视频生成需要更长时间，先等待40秒后开始检查结果...
                logger.info("视频生成需要较长时间，等待60秒后开始检查结果...")
                proxy_logs.append("视频生成中，请耐心等待...")
                time.sleep(40)

                # 轮询等待结果
                logger.info("开始检查视频生成结果...")
                proxy_logs.append("正在检查生成进度...")
                poll_count = 0
                max_poll_time = 180  # 最大轮询时间10分钟
                start_poll_time = beijing_time_timestamp()

                while True:
                    # 检查是否超时
                    elapsed_time = beijing_time_timestamp() - start_poll_time
                    if elapsed_time > max_poll_time:
                        timeout_msg = f"视频生成超时（{max_poll_time}秒），停止检查"
                        logger.error(timeout_msg)
                        proxy_logs.append("生成时间过长，请稍后重试")
                        return None, "生成时间过长，请稍后重试"

                    try:
                        # 添加超时和异常处理的网络请求
                        result_response = requests.get(result_url, timeout=30, proxies=proxies)
                        result_text = result_response.text

                        # 记录响应状态
                        logger.debug(f"轮询响应状态码: {result_response.status_code}")

                        # 检查响应是否为空
                        if not result_text or result_text.strip() == "":
                            logger.warning("收到空响应，继续等待...")
                            poll_count += 1
                            if poll_count % 10 == 0:
                                status_msg = f"收到空响应，仍在等待视频生成结果...({poll_count * 6}秒)"
                                logger.info(status_msg)
                                proxy_logs.append(f"生成中...({poll_count * 6}秒)")
                            time.sleep(6)
                            continue

                        # 记录响应内容的前100个字符用于调试
                        logger.debug(f"响应内容预览: {result_text[:100]}...")

                    except requests.exceptions.Timeout:
                        logger.warning("请求超时，继续重试...")
                        poll_count += 1
                        time.sleep(6)
                        continue
                    except requests.exceptions.RequestException as e:
                        logger.error(f"网络请求异常: {e}")
                        poll_count += 1
                        if poll_count > 5:  # 连续5次网络错误后放弃
                            proxy_logs.append("网络连接不稳定，生成失败")
                            return None, "网络连接不稳定，生成失败"
                        time.sleep(6)
                        continue

                    # 定期更新状态
                    poll_count += 1
                    if poll_count % 10 == 0:
                        status_msg = f"仍在等待视频生成结果...({poll_count * 6}秒，总计{int(elapsed_time)}秒)"
                        logger.info(status_msg)
                        proxy_logs.append(f"生成中...已等待{int(elapsed_time)}秒")

                    # 处理SSE格式的响应
                    if "event: complete" in result_text:
                        logger.info("视频生成完成")
                        proxy_logs.append("视频生成完成！")
                        # 提取complete事件的data部分
                        complete_start = result_text.find("event: complete")
                        if complete_start != -1:
                            # 从complete事件开始查找data
                            data_start = result_text.find("data: ", complete_start)
                            if data_start != -1:
                                data_json_str = result_text[data_start + 6:].strip()
                                # 如果有后续event行，只取到下一个event之前
                                next_event = data_json_str.find("\nevent:")
                                if next_event != -1:
                                    data_json_str = data_json_str[:next_event].strip()

                                # 添加调试信息
                                logger.info(f"收到的数据: {data_json_str[:200]}...")  # 只显示前200个字符
                                proxy_logs.append("正在处理生成结果...")

                                try:
                                    # 解析JSON数组
                                    result_data = json.loads(data_json_str)
                                    if result_data and len(result_data) >= 1:
                                        # 视频结果在第一个元素的video字段中
                                        video_data = result_data[0]
                                        if isinstance(video_data, dict) and "video" in video_data:
                                            video_info = video_data["video"]
                                            if isinstance(video_info, dict) and "url" in video_info:
                                                video_url = video_info["url"]
                                                video_id = result_data[1] if len(result_data) > 1 else f"video_{int(time.time())}"

                                                logger.info(f"视频URL: {video_url}")
                                                logger.info(f"视频ID: {video_id}")

                                                return {
                                                    "video_url": video_url,
                                                    "video_id": video_id
                                                }, "成功生成视频"
                                            else:
                                                proxy_logs.append("视频生成失败，请重试")
                                                return None, "视频生成失败，请重试"
                                        else:
                                            proxy_logs.append("视频生成失败，请重试")
                                            return None, "视频生成失败，请重试"
                                    else:
                                        proxy_logs.append("视频生成失败，请重试")
                                        return None, "视频生成失败，请重试"
                                except json.JSONDecodeError as e:
                                    logger.error(f"解析JSON数据失败: {e}")
                                    proxy_logs.append("视频生成失败，请重试")
                                    return None, "视频生成失败，请重试"
                            else:
                                proxy_logs.append("视频生成失败，请重试")
                                return None, "视频生成失败，请重试"
                        else:
                            proxy_logs.append("视频生成失败，请重试")
                            return None, "视频生成失败，请重试"
                    elif "event: heartbeat" in result_text:
                        # 心跳事件，正常情况，继续等待
                        logger.debug("收到heartbeat事件，继续等待...")
                        # 检查heartbeat事件的data部分
                        heartbeat_start = result_text.find("event: heartbeat")
                        if heartbeat_start != -1:
                            data_start = result_text.find("data: ", heartbeat_start)
                            if data_start != -1:
                                heartbeat_data = result_text[data_start + 6:].strip()
                                next_event = heartbeat_data.find("\nevent:")
                                if next_event != -1:
                                    heartbeat_data = heartbeat_data[:next_event].strip()
                                logger.debug(f"Heartbeat数据: {heartbeat_data}")
                                # 如果heartbeat返回null，这是正常的
                                if heartbeat_data == "null":
                                    logger.debug("收到正常的heartbeat null数据")
                    elif "event: error" in result_text:
                        logger.error("视频生成过程出错！")
                        proxy_logs.append("生成过程出现错误")
                        # 提取error事件的data部分
                        error_start = result_text.find("event: error")
                        if error_start != -1:
                            data_start = result_text.find("data: ", error_start)
                            if data_start != -1:
                                error_data = result_text[data_start + 6:].strip()
                                # 如果有后续event行，只取到下一个event之前
                                next_event = error_data.find("\nevent:")
                                if next_event != -1:
                                    error_data = error_data[:next_event].strip()
                                logger.error(f"错误信息: {error_data}")

                        retry_count += 1
                        if retry_count < max_retries:
                            logger.info(f"正在进行第{retry_count+1}次重试...")
                            proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                            break
                        else:
                            proxy_logs.append("多次重试失败，请稍后再试")
                            return None, "多次重试失败，请稍后再试"
                    elif "event: estimation" in result_text:
                        # 估算时间事件
                        logger.debug("收到estimation事件")
                        estimation_start = result_text.find("event: estimation")
                        if estimation_start != -1:
                            data_start = result_text.find("data: ", estimation_start)
                            if data_start != -1:
                                estimation_data = result_text[data_start + 6:].strip()
                                next_event = estimation_data.find("\nevent:")
                                if next_event != -1:
                                    estimation_data = estimation_data[:next_event].strip()
                                logger.info(f"预估等待时间: {estimation_data}")
                                proxy_logs.append("正在排队等待处理...")
                    elif "event: process_starts" in result_text:
                        # 处理开始事件
                        logger.info("视频生成处理开始")
                        proxy_logs.append("开始生成视频...")
                    elif "event: progress" in result_text:
                        # 进度事件
                        logger.debug("收到progress事件")
                        progress_start = result_text.find("event: progress")
                        if progress_start != -1:
                            data_start = result_text.find("data: ", progress_start)
                            if data_start != -1:
                                progress_data = result_text[data_start + 6:].strip()
                                next_event = progress_data.find("\nevent:")
                                if next_event != -1:
                                    progress_data = progress_data[:next_event].strip()
                                logger.debug(f"进度信息: {progress_data}")
                                # 每10次进度更新才记录一次到proxy_logs
                                if poll_count % 10 == 0:
                                    proxy_logs.append("生成中...")
                    elif "event:" in result_text:
                        # 其他类型的事件，记录但继续等待
                        event_lines = [line for line in result_text.split('\n') if line.startswith('event:')]
                        if event_lines:
                            event_type = event_lines[0].replace('event: ', '').strip()
                            logger.debug(f"收到其他SSE事件: {event_type}")
                            # 只记录未知的事件类型
                            if event_type not in ['heartbeat', 'complete', 'error', 'estimation', 'process_starts', 'progress']:
                                logger.info(f"收到未知事件类型: {event_type}")
                    else:
                        # 没有识别到任何事件，记录响应内容用于调试
                        logger.warning(f"未识别到SSE事件格式，响应内容: {result_text[:200]}...")
                        if poll_count % 20 == 0:  # 每20次才记录一次，避免日志过多
                            logger.debug(f"未识别的响应格式: {result_text[:100]}...")

                    # 视频生成需要更长时间，等待6秒再次查询
                    time.sleep(6)

            except Exception as e:
                logger.error(f"视频生成过程中发生错误: {e}")
                proxy_logs.append("生成过程出现错误")
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"正在进行第{retry_count+1}次重试...")
                    proxy_logs.append(f"正在重试...({retry_count+1}/{max_retries})")
                    continue
                return None, "多次重试失败，请稍后再试"

    finally:
        # 无论成功还是失败，都要清除队列状态
        with video_queue_locks[model_key]:
            video_queues[model_key]['is_processing'] = False
            video_queues[model_key]['start_time'] = None
            video_queues[model_key]['current_user_info'] = None
            logger.info(f"视频生成任务结束，模型 {video_model['display_name']} 队列状态已清除")

    return None, "生成视频失败"

@app.route('/')
def index():
    # 获取当前用户信息
    current_user = None
    if 'username' in session:
        current_user = user_manager.get_user(session['username'])

        # 检查用户是否有未读站内信
        if current_user:
            unread_count = message_system.get_unread_count(session['username'])
            if unread_count > 0:
                # 如果有未读消息，强制跳转到站内信页面
                return redirect(url_for('messages_page', force_read=1))

    return render_template('index.html', models=MODELS, current_user=current_user)

@app.route('/login')
def login_page():
    """独立的登录页面"""
    # 如果用户已经登录，重定向到首页
    if 'username' in session:
        return redirect(url_for('index'))

    return render_template('login.html')

# ==================== 辅助函数 ====================

def simulate_cleanup_stats(generation_history_manager, max_records, days_to_keep):
    """模拟清理操作，计算统计信息但不实际修改数据"""
    cleaned_users = 0
    total_records_before = 0
    total_records_after = 0

    cutoff_date = datetime.now() - timedelta(days=days_to_keep)

    for username, records in generation_history_manager.history_data.items():
        records_before = len(records)
        total_records_before += records_before

        # 模拟清理过期记录
        filtered_history = []
        for record in records:
            try:
                record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                if record_time.replace(tzinfo=None) > cutoff_date:
                    filtered_history.append(record)
            except (ValueError, KeyError):
                filtered_history.append(record)

        # 模拟限制记录数量
        if len(filtered_history) > max_records:
            filtered_history = filtered_history[-max_records:]

        records_after = len(filtered_history)
        total_records_after += records_after

        if records_before > records_after:
            cleaned_users += 1

    return {
        'cleaned_users': cleaned_users,
        'total_records_before': total_records_before,
        'total_records_after': total_records_after,
        'records_removed': total_records_before - total_records_after
    }

def get_history_statistics(generation_history_manager):
    """获取历史记录统计信息"""
    return generation_history_manager.get_history_statistics()

# ==================== 管理员功能 ====================

@app.route('/admin')
@login_required
def admin_panel():
    """管理员面板页面"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return redirect(url_for('index'))

    return render_template('admin.html', current_user=user)

@app.route('/redeem')
@login_required
def redeem_page():
    """兑换码页面"""
    username = session.get('username')
    if not username:
        return redirect(url_for('index'))

    user = user_manager.get_user(username)
    if not user:
        return redirect(url_for('index'))

    return render_template('redeem.html', current_user=user)

@app.route('/apply')
@login_required
def apply_page():
    """申请页面"""
    username = session.get('username')
    if not username:
        return redirect(url_for('index'))

    user = user_manager.get_user(username)
    if not user:
        return redirect(url_for('index'))

    return render_template('apply.html', current_user=user)

@app.route('/imagelist')
@login_required
def gallery_page():
    """画廊页面 - 授权用户可以看到所有人的画廊记录，未授权用户只能看到自己的记录"""
    username = session.get('username')
    user = user_manager.get_user(username)

    # 所有登录用户都可以访问画廊页面
    # 不再在服务器端获取数据，改为客户端通过API获取并分页
    return render_template('gallery.html', records=[], current_user=user)

def apply_image_proxy(image_url, proxy_enabled):
    """为图片URL添加代理前缀"""
    if not proxy_enabled or not image_url:
        return image_url

    # 如果URL已经包含代理前缀，则不重复添加
    if image_url.startswith('https://i0.wp.com/'):
        return image_url

    # 如果是相对路径或本地路径，不添加代理
    if not image_url.startswith('http'):
        return image_url

    # 添加代理前缀，去掉原URL的协议部分
    if image_url.startswith('https://'):
        return f"https://i0.wp.com/{image_url[8:]}"  # 去掉 "https://"
    elif image_url.startswith('http://'):
        return f"https://i0.wp.com/{image_url[7:]}"   # 去掉 "http://"

    return image_url

@app.route('/api/gallery')
@login_required
def gallery_api():
    """画廊API - 返回JSON格式的画廊数据，支持翻页和搜索
    授权用户可以看到所有人的记录，未授权用户只能看到自己的记录"""
    username = session.get('username')

    hours = request.args.get('hours', 1, type=int)
    limit = request.args.get('limit', 42, type=int)  # 默认每页42条（能被3整除）
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str).strip()

    # 限制参数范围
    hours = max(1, min(hours, 168))  # 1-168小时（7天）
    limit = max(1, min(limit, 1000))  # 1-1000条记录
    page = max(1, page)

    # 根据用户授权状态获取不同的记录
    if user_manager.is_user_authorized(username):
        # 授权用户可以看到所有人的记录
        recent_records = generation_history_manager.get_recent_gallery_records(
            hours=hours,
            limit=limit * 10,  # 获取更多记录用于搜索和分页
            search=search
        )
    else:
        # 未授权用户只能看到自己的记录
        recent_records = generation_history_manager.get_user_gallery_records(
            username=username,
            hours=hours,
            limit=limit * 10,
            search=search
        )

    # 获取图片代理设置
    settings = points_system.get_settings()
    proxy_enabled = settings.get('gallery_image_proxy_enabled', False)

    # 处理图片代理
    for record in recent_records:
        if record.get('image_url'):
            record['image_url'] = apply_image_proxy(record['image_url'], proxy_enabled)

    # 计算分页
    total_records = len(recent_records)
    start_index = (page - 1) * limit
    end_index = start_index + limit
    paginated_records = recent_records[start_index:end_index]

    total_pages = (total_records + limit - 1) // limit  # 向上取整

    return jsonify({
        'success': True,
        'records': paginated_records,
        'count': len(paginated_records),
        'total_count': total_records,
        'page': page,
        'total_pages': total_pages,
        'has_next': page < total_pages,
        'has_prev': page > 1,
        'hours': hours,
        'limit': limit,
        'search': search
    })

@app.route('/submit_application', methods=['POST'])
@login_required
def submit_application():
    """提交申请"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    user = user_manager.get_user(username)
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    try:
        data = request.get_json()
        application_type = data.get('type', '').strip()

        if application_type not in ['points', 'feedback']:
            return jsonify({'success': False, 'message': '无效的申请类型'})

        # 验证申请数据
        if application_type == 'points':
            points_amount = data.get('points_amount', 0)
            reason = data.get('reason', '').strip()

            if not isinstance(points_amount, int) or points_amount <= 0 or points_amount > 10000:
                return jsonify({'success': False, 'message': '点数数量必须在1-10000之间'})

            if not reason or len(reason) < 10:
                return jsonify({'success': False, 'message': '申请理由至少需要10个字符'})

            application_data = {
                'points_amount': points_amount,
                'reason': reason
            }

        elif application_type == 'feedback':
            feedback_type = data.get('feedback_type', '').strip()
            title = data.get('title', '').strip()
            content = data.get('content', '').strip()

            if not feedback_type or feedback_type not in ['bug', 'feature', 'improvement', 'question', 'other']:
                return jsonify({'success': False, 'message': '请选择有效的反馈类型'})

            if not title or len(title) < 5:
                return jsonify({'success': False, 'message': '标题至少需要5个字符'})

            if not content or len(content) < 10:
                return jsonify({'success': False, 'message': '内容至少需要10个字符'})

            application_data = {
                'feedback_type': feedback_type,
                'title': title,
                'content': content
            }

        # 提交申请
        success, message = application_system.submit_application(
            username=username,
            application_type=application_type,
            data=application_data,
            user_info=user
        )

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"提交申请时发生错误: {e}")
        return jsonify({'success': False, 'message': '申请提交失败，请稍后重试'})

@app.route('/send_verification_code', methods=['POST'])
def send_verification_code():
    """发送邮箱验证码"""
    data = request.get_json()
    email = data.get('email', '').strip()

    if not email:
        return jsonify({'success': False, 'message': '邮箱地址不能为空'})

    # 验证邮箱格式
    import re
    email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    if not re.match(email_regex, email):
        return jsonify({'success': False, 'message': '请输入有效的邮箱地址'})

    # 限制只能使用QQ邮箱
    if not email.lower().endswith('@qq.com'):
        return jsonify({'success': False, 'message': '仅支持QQ邮箱（@qq.com）'})

    # 检查邮箱是否已被注册
    for username, user_data in user_manager.users.items():
        if user_data.get('email', '').lower() == email.lower():
            return jsonify({'success': False, 'message': '该邮箱已被注册'})

    try:
        # 清理过期的验证码
        user_manager.cleanup_expired_codes()

        # 生成验证码
        code = user_manager.generate_verification_code(email)

        # 发送邮件
        success, message = user_manager.send_verification_email(email, code)

        if success:
            return jsonify({
                'success': True,
                'message': '验证码已发送到您的邮箱，请查收（有效期5分钟）'
            })
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        logger.error(f"发送验证码失败: {e}")
        return jsonify({'success': False, 'message': '发送验证码失败，请稍后重试'})

@app.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '').strip()
    email = data.get('email', '').strip()
    reason = data.get('reason', '').strip()
    verification_code = data.get('verification_code', '').strip()

    if not username or not password or not email or not reason or not verification_code:
        return jsonify({'success': False, 'message': '所有字段都是必填项'})

    # 验证邮箱格式和域名限制
    import re
    email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
    if not re.match(email_regex, email):
        return jsonify({'success': False, 'message': '请输入有效的邮箱地址'})

    # 限制只能使用QQ邮箱注册
    if not email.lower().endswith('@qq.com'):
        return jsonify({'success': False, 'message': '注册仅限使用QQ邮箱（@qq.com）'})

    # 验证申请理由长度
    if len(reason) < 10:
        return jsonify({'success': False, 'message': '申请理由至少需要10个字符'})

    # 验证邮箱验证码
    code_valid, code_message = user_manager.verify_email_code(email, verification_code)
    if not code_valid:
        return jsonify({'success': False, 'message': code_message})

    # 获取系统设置，检查是否需要审核
    settings = points_system.get_settings()
    require_approval = settings.get('require_registration_approval', False)

    success, message = user_manager.register_user(username, password, email, reason, require_approval, email_verified=True)

    if success:
        if require_approval:
            # 需要审核的情况下，不自动登录
            return jsonify({
                'success': True,
                'message': message,
                'require_approval': True
            })
        else:
            # 不需要审核，注册成功后自动登录
            session['username'] = username
            user_stats = user_manager.get_user_stats(username)
            return jsonify({
                'success': True,
                'message': message,
                'user': user_stats,
                'require_approval': False
            })
    else:
        return jsonify({'success': False, 'message': message})

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '').strip()

    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'})

    success, message = user_manager.login_user(username, password)

    if success:
        session['username'] = username
        user_stats = user_manager.get_user_stats(username)
        return jsonify({
            'success': True,
            'message': message,
            'user': user_stats
        })
    else:
        return jsonify({'success': False, 'message': message})

@app.route('/logout', methods=['POST'])
def logout():
    """用户退出登录"""
    try:
        # 记录退出登录的用户
        username = session.get('username')
        if username:
            logger.info(f"用户 {username} 退出登录")

        # 清除session中的所有用户相关信息
        session.pop('username', None)
        session.pop('oauth_state', None)  # 清除OAuth状态

        # 清除整个session以确保完全退出
        session.clear()

        return jsonify({'success': True, 'message': '已退出登录'})
    except Exception as e:
        logger.error(f"退出登录时发生错误: {e}")
        # 即使出错也要清除session
        session.clear()
        return jsonify({'success': True, 'message': '已退出登录'})

@app.route('/auth/linux_do')
def linux_do_auth():
    """跳转到Linux Do OAuth2授权页面"""
    auth_url = get_linux_do_auth_url()
    return redirect(auth_url)

@app.route('/linux')
def linux_do_callback():
    """Linux Do OAuth2回调处理"""
    # 获取授权码和状态参数
    code = request.args.get('code')
    state = request.args.get('state')
    error = request.args.get('error')

    # 检查是否有错误
    if error:
        logger.error(f"Linux Do OAuth2授权失败: {error}")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('授权失败，请重试'))

    # 检查必要参数
    if not code or not state:
        logger.error("Linux Do OAuth2回调缺少必要参数")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('授权参数错误'))

    # 验证状态参数，防止CSRF攻击
    if state != session.get('oauth_state'):
        logger.error("Linux Do OAuth2状态参数验证失败")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('授权状态验证失败'))

    # 清除状态参数
    session.pop('oauth_state', None)

    # 获取访问令牌
    token_data = get_linux_do_access_token(code)
    if not token_data or 'access_token' not in token_data:
        logger.error("获取Linux Do访问令牌失败")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('获取访问令牌失败'))

    access_token = token_data['access_token']

    # 获取用户信息
    user_info = get_linux_do_user_info(access_token)
    if not user_info:
        logger.error("获取Linux Do用户信息失败")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('获取用户信息失败'))

    # 提取用户信息
    linux_do_id = str(user_info.get('id', ''))
    username = user_info.get('username', '')
    name = user_info.get('name', '')
    email = user_info.get('email', '')
    avatar_template = user_info.get('avatar_template', '')

    if not linux_do_id or not username:
        logger.error("Linux Do用户信息不完整")
        return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('用户信息不完整'))

    # 生成本地用户名（使用Linux Do用户名，如果冲突则添加后缀）
    local_username = f"linuxdo_{username}"
    original_username = local_username
    counter = 1

    # 检查用户是否已存在（通过Linux Do ID）
    existing_user = None
    for uname, udata in user_manager.users.items():
        if udata.get('linux_do_id') == linux_do_id:
            existing_user = uname
            break

    if existing_user:
        # 用户已存在，直接登录
        session['username'] = existing_user
        logger.info(f"Linux Do用户 {username} (ID: {linux_do_id}) 登录成功")
        return redirect(url_for('index'))
    else:
        # 新用户，需要注册
        # 确保用户名不冲突
        while user_manager.get_user(local_username):
            counter += 1
            local_username = f"{original_username}_{counter}"

        # 创建新用户
        success = user_manager.create_linux_do_user(
            username=local_username,
            linux_do_id=linux_do_id,
            linux_do_username=username,
            name=name,
            email=email,
            avatar_template=avatar_template
        )

        if success:
            session['username'] = local_username
            logger.info(f"Linux Do用户 {username} (ID: {linux_do_id}) 注册并登录成功，本地用户名: {local_username}")
            return redirect(url_for('index'))
        else:
            logger.error(f"创建Linux Do用户失败: {username} (ID: {linux_do_id})")
            return redirect(url_for('login_page') + '?error=oauth_error&message=' + urllib.parse.quote('用户注册失败'))

@app.route('/userinfo')
@login_required
def userinfo_page():
    """用户个人中心页面"""
    username = session.get('username')
    if not username:
        return redirect(url_for('index'))

    user = user_manager.get_user(username)
    if not user:
        return redirect(url_for('index'))

    # 获取用户统计信息
    user_stats = user_manager.get_user_stats(username)

    # 计算用户总共花费的积分
    user_transactions = points_system.get_user_transactions(username, limit=1000)  # 获取所有交易记录
    total_spent_points = sum(abs(t['points_change']) for t in user_transactions if t['points_change'] < 0)

    # 统计生成的视频和图片数量
    generation_history = user.get('generation_history', [])
    total_images = sum(1 for record in generation_history if record.get('type') == 'image' and record.get('success', False))
    total_videos = sum(1 for record in generation_history if record.get('type') == 'video' and record.get('success', False))

    # 获取未读站内信数量
    unread_messages_count = message_system.get_unread_count(username)

    # 获取API密钥信息
    api_key_info = user_manager.get_api_key(username)

    # 准备传递给模板的数据
    user_info = {
        'username': user['username'],
        'points': user['points'],
        'total_spent_points': total_spent_points,
        'total_images': total_images,
        'total_videos': total_videos,
        'total_generated': user.get('total_generated', 0),
        'created_at': user.get('created_at', ''),
        'last_login': user.get('last_login', ''),
        'recent_transactions': user_transactions[:10],  # 最近10条交易记录
        'recent_history': generation_history[-10:] if generation_history else [],  # 最近10条生成记录
        'unread_messages_count': unread_messages_count,  # 未读站内信数量
        'api_key_info': api_key_info,  # API密钥信息
        'generation_cost': points_system.get_generation_cost('image')  # 图像生成成本
    }

    return render_template('userinfo.html',
                         user_info=user_info,
                         current_user=user,
                         models=MODELS,
                         site_domain=get_site_domain())

@app.route('/api/generate_api_key', methods=['POST'])
@login_required
def generate_api_key():
    """生成API密钥"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        success, result = user_manager.generate_api_key(username)
        if success:
            return jsonify({
                'success': True,
                'message': 'API密钥生成成功',
                'api_key': result
            })
        else:
            return jsonify({'success': False, 'message': result})
    except Exception as e:
        logger.error(f"生成API密钥失败: {e}")
        return jsonify({'success': False, 'message': '生成API密钥失败'})

@app.route('/api/delete_api_key', methods=['POST'])
@login_required
def delete_api_key():
    """删除API密钥"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        success, message = user_manager.delete_api_key(username)
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"删除API密钥失败: {e}")
        return jsonify({'success': False, 'message': '删除API密钥失败'})

@app.route('/api/v1/generate_image', methods=['POST'])
def api_generate_image():
    """API接口：生成图像"""
    # 验证API密钥
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '缺少或无效的Authorization头'}), 401

    api_key = auth_header.replace('Bearer ', '')
    user_info = user_manager.verify_api_key(api_key)
    if not user_info:
        return jsonify({'error': '无效的API密钥'}), 401

    username = user_info['username']
    user = user_info['user_data']

    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求体必须是JSON格式'}), 400

        # 获取参数
        prompt = data.get('prompt', '').strip()
        negative_prompt = data.get('negative_prompt', '').strip()
        width = data.get('width', 512)
        height = data.get('height', 512)
        steps = data.get('steps', 20)
        cfg = data.get('cfg', 7)
        model_index = data.get('model_index', 0)
        seed = data.get('seed', -1)
        enable_hr = data.get('enable_hr', False)
        restore_faces = data.get('restore_faces', False)

        # 验证参数
        if not prompt:
            return jsonify({'error': '提示词不能为空'}), 400

        if len(prompt) > 2000:
            return jsonify({'error': '提示词长度不能超过2000个字符'}), 400

        # 验证模型索引
        if model_index < 0 or model_index >= len(MODELS):
            return jsonify({'error': f'模型索引无效，应在0-{len(MODELS)-1}之间'}), 400

        # 检查是否为图像模型
        if MODELS[model_index]['type'] != 'image':
            return jsonify({'error': '指定的模型不是图像生成模型'}), 400

        # 检查用户积分
        generation_cost = points_system.get_generation_cost('image')
        if user['points'] < generation_cost:
            return jsonify({'error': f'积分不足，需要{generation_cost}积分'}), 400

        # 生成图像
        result, message = generate_image(
            prompt=prompt,
            negative_prompt=negative_prompt,
            width=width,
            height=height,
            steps=steps,
            cfg=cfg,
            model_index=model_index,
            seed=seed,
            enable_hr=enable_hr,
            restore_faces=restore_faces
        )

        if result:
            # 扣除积分
            success, points_message = user_manager.update_user_points(username, -generation_cost)
            if success:
                # 添加生成记录
                selected_model = MODELS[model_index]
                generation_history_manager.add_generation_record(
                    username=username,
                    generation_type='image',
                    prompt=prompt,
                    success=True,
                    negative_prompt=negative_prompt,
                    image_url=result["image_url"],
                    model_name=selected_model['display_name'],
                    width=width,
                    height=height,
                    steps=steps,
                    cfg=cfg,
                    seed=seed,
                    enable_hr=enable_hr,
                    restore_faces=restore_faces,
                    generation_source='api'
                )

                # 更新用户生成计数
                user_manager.increment_generation_count(username)

                # 记录积分交易
                points_system.add_transaction(username, -generation_cost, 'generation', f'API生成图像 - {selected_model["display_name"]}')

                return jsonify({
                    'success': True,
                    'message': '图像生成成功',
                    'data': {
                        'image_url': result["image_url"],
                        'image_id': result["image_id"],
                        'model_name': selected_model['display_name'],
                        'points_used': generation_cost,
                        'remaining_points': user['points'] - generation_cost
                    }
                })
            else:
                return jsonify({'error': f'积分扣除失败: {points_message}'}), 500
        else:
            return jsonify({'error': f'图像生成失败: {message}'}), 500

    except Exception as e:
        logger.error(f"API生成图像失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@app.route('/user_info')
def user_info():
    if 'username' not in session:
        return jsonify({'success': False, 'message': '未登录'})

    username = session['username']
    user_stats = user_manager.get_user_stats(username)
    if user_stats:
        # 添加最近的历史记录
        recent_history = generation_history_manager.get_user_history(username, limit=10)
        user_stats['recent_history'] = recent_history
        return jsonify({'success': True, 'user': user_stats})
    else:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

@app.route('/random_tag')
def random_tag():
    tag = load_random_tag()
    return jsonify({"tag": tag})

@app.route('/video_queue_status')
def video_queue_status_api():
    """检查视频生成队列状态"""
    model_index = request.args.get('model_index', type=int)

    # 确保队列已初始化
    init_video_queues()

    if model_index is not None:
        # 查询特定模型的队列状态
        model_key = f"video_model_{model_index}"
        if model_key in video_queues and model_key in video_queue_locks:
            with video_queue_locks[model_key]:
                queue_info = video_queues[model_key]
                if queue_info['is_processing']:
                    elapsed_time = int(beijing_time_timestamp() - queue_info['start_time']) if queue_info['start_time'] else 0
                    return jsonify({
                        "is_processing": True,
                        "elapsed_time": elapsed_time,
                        "model_name": queue_info['model_name'],
                        "message": f"模型 {queue_info['model_name']} 前面已经有人在排队了，已等待 {elapsed_time} 秒，请等待一段时间再试"
                    })
                else:
                    return jsonify({
                        "is_processing": False,
                        "model_name": queue_info['model_name'],
                        "message": f"模型 {queue_info['model_name']} 队列空闲，可以开始生成"
                    })
        else:
            return jsonify({
                "error": "模型不可用"
            })
    else:
        # 查询所有视频模型的队列状态
        all_queues = {}
        for model_key, queue_info in video_queues.items():
            with video_queue_locks[model_key]:
                if queue_info['is_processing']:
                    elapsed_time = int(beijing_time_timestamp() - queue_info['start_time']) if queue_info['start_time'] else 0
                    all_queues[model_key] = {
                        "is_processing": True,
                        "elapsed_time": elapsed_time,
                        "model_name": queue_info['model_name'],
                        "model_url": queue_info['model_url']
                    }
                else:
                    all_queues[model_key] = {
                        "is_processing": False,
                        "model_name": queue_info['model_name'],
                        "model_url": queue_info['model_url']
                    }

        return jsonify({
            "queues": all_queues,
            "message": "所有视频模型队列状态"
        })



@app.route('/generate', methods=['POST'])
@login_required
def generate():
    # 检查用户登录状态
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    # 获取用户信息
    user = user_manager.get_user(username)
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    # 检查积分是否足够
    can_afford, cost_message = points_system.can_afford_generation(username, 'image', user['points'])
    if not can_afford:
        return jsonify({'success': False, 'message': cost_message})

    data = request.form

    # 获取参数
    prompt = data.get('prompt', '')
    if data.get('append_default_prompt') == 'true':
        prompt += ",masterpiece, best quality, absurdres, newest, very aesthetic, amazing quality,highres,sensitive, highres, ultra detailed, best anatomy, HDR, 8K, high detail RAW color art, high contrast, "

    negative_prompt = data.get('negative_prompt', 'lowres, {bad}, error, fewer, extra, missing, worst quality, jpeg artifacts, bad quality, watermark, unfinished, displeasing, chromatic aberration, signature, extra digits, artistic error, username, scan, [abstract]')
    width = int(data.get('width', 832))
    height = int(data.get('height', 1216))
    steps = int(data.get('steps', 28))
    cfg = int(data.get('cfg', 7))
    enable_hr = data.get('enable_hr') == 'true'
    restore_faces = data.get('restore_faces') == 'true'
    seed = int(data.get('seed', 0))
    model_index = int(data.get('model_index', 0))
    # 统一使用管理员配置的代理池，无需用户选择

    # 启动生成任务
    result, message = {}, "开始生成..."

    try:
        # 创建线程执行生成任务
        thread = threading.Thread(
            target=lambda: None  # 实际部署时，应确保任务在后台执行，此处简化
        )
        thread.daemon = True
        thread.start()

        # 同步执行生成（实际应用中应考虑异步处理）
        result, message = generate_image(
            prompt, negative_prompt, width, height, steps, cfg,
            model_index, seed, enable_hr, restore_faces
        )

        if result:
            # 生成成功，扣除积分
            success, points_message, new_points = points_system.process_generation(
                username, 'image', prompt, user['points']
            )

            if success:
                # 更新用户积分
                user_manager.update_user_points(username, -points_system.get_generation_cost('image'))
                # 添加生成记录，包含更多信息
                selected_model = MODELS[model_index]
                generation_history_manager.add_generation_record(
                    username=username,
                    generation_type='image',
                    prompt=prompt,
                    success=True,
                    negative_prompt=negative_prompt,
                    image_url=result["image_url"],
                    model_name=selected_model['display_name'],
                    width=width,
                    height=height,
                    steps=steps,
                    cfg=cfg,
                    seed=seed,
                    enable_hr=enable_hr,
                    restore_faces=restore_faces,
                    generation_source='web'
                )

                # 更新用户生成计数
                user_manager.increment_generation_count(username)

                return jsonify({
                    "success": True,
                    "message": f"{message} {points_message}",
                    "image_url": result["image_url"],  # 直接使用云端URL
                    "image_id": result["image_id"],
                    "proxy_logs": proxy_logs,  # 添加代理日志信息
                    "user_points": new_points  # 返回更新后的积分
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"积分扣除失败: {points_message}",
                    "proxy_logs": proxy_logs
                })
        else:
            # 生成失败，记录失败记录但不扣积分
            selected_model = MODELS[model_index]
            generation_history_manager.add_generation_record(
                username=username,
                generation_type='image',
                prompt=prompt,
                success=False,
                negative_prompt=negative_prompt,
                model_name=selected_model['display_name'],
                width=width,
                height=height,
                steps=steps,
                cfg=cfg,
                seed=seed,
                enable_hr=enable_hr,
                restore_faces=restore_faces,
                generation_source='web'
            )
            return jsonify({
                "success": False,
                "message": message,
                "proxy_logs": proxy_logs  # 添加代理日志信息
            })
    except Exception as e:
        logger.error(f"生成图像时发生错误: {e}")
        return jsonify({
            "success": False,
            "message": f"生成图像时发生错误: {e}"
        })

@app.route('/generate_video', methods=['POST'])
@login_required
def generate_video_route():
    # 检查用户登录状态
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    # 获取用户信息
    user = user_manager.get_user(username)
    if not user:
        return jsonify({'success': False, 'message': '用户信息获取失败'})

    # 检查积分是否足够
    can_afford, cost_message = points_system.can_afford_generation(username, 'video', user['points'])
    if not can_afford:
        return jsonify({'success': False, 'message': cost_message})

    data = request.form

    # 获取模型索引，如果没有指定则使用第一个视频模型
    model_index = int(data.get('model_index', 0))

    # 确保队列已初始化
    init_video_queues()

    # 检查指定模型是否是视频模型
    if model_index >= len(MODELS) or MODELS[model_index]['type'] != 'video':
        # 如果指定的模型不是视频模型，找到第一个视频模型
        model_index = next((i for i, model in enumerate(MODELS) if model['type'] == 'video'), None)
        if model_index is None:
            return jsonify({
                "success": False,
                "message": "视频生成服务暂不可用",
                "proxy_logs": ["视频生成服务暂不可用"]
            })

    model_key = f"video_model_{model_index}"
    video_model = MODELS[model_index]

    # 确保该模型的队列已初始化
    if model_key not in video_queues:
        video_queues[model_key] = {
            'is_processing': False,
            'start_time': None,
            'current_user_info': None,
            'model_name': video_model['display_name'],
            'model_url': video_model['source_url']
        }
        video_queue_locks[model_key] = threading.Lock()

    # 检查该模型的队列状态
    with video_queue_locks[model_key]:
        if video_queues[model_key]['is_processing']:
            elapsed_time = int(beijing_time_timestamp() - video_queues[model_key]['start_time']) if video_queues[model_key]['start_time'] else 0
            return jsonify({
                "success": False,
                "message": f"模型 {video_model['display_name']} 前面已经有人在排队了，已等待 {elapsed_time} 秒，请等待一段时间再试",
                "proxy_logs": [f"当前有其他用户正在使用，请稍后再试"],
                "queue_busy": True,
                "model_name": video_model['display_name']
            })

    # 记录接收到的所有表单字段
    logger.info("接收到视频生成请求，表单数据:")
    for key, value in data.items():
        if key == 'image_source' and len(value) > 100:
            logger.info(f"- {key}: [图像数据，长度 {len(value)} 字符]")
        else:
            logger.info(f"- {key}: {value}")

    # 获取参数
    image_source = data.get('image_source', '')  # 可以是URL或base64数据
    prompt = data.get('prompt', 'make this image come alive, cinematic motion, smooth animation')
    width = int(data.get('width', 512))
    height = int(data.get('height', 896))
    negative_prompt = data.get('negative_prompt', 'Bright tones, overexposed, static, blurred details, subtitles, style, works, paintings, images, static, overall gray, worst quality, low quality, JPEG compression residue, ugly, incomplete, extra fingers, poorly drawn hands, poorly drawn faces, deformed, disfigured, misshapen limbs, fused fingers, still picture, messy background, three legs, many people in the background, walking backwards, watermark, text, signature')
    motion_bucket_id = int(data.get('motion_bucket_id', 2))
    cond_aug = int(data.get('cond_aug', 1))
    steps = int(data.get('steps', 4))
    seed = int(data.get('seed', 0))
    # 统一使用管理员配置的代理池，无需用户选择

    # 添加日志，说明宽高将根据输入图像自动调整
    logger.info(f"前端传入的宽高参数 {width}x{height} 将根据输入图像比例自动调整，单边最大值不超过896，且保证能被32整除")
    proxy_logs.append("正在准备视频生成...")

    if not image_source:
        logger.error("未提供图片源")
        return jsonify({
            "success": False,
            "message": "请先上传图片或提供图片链接",
            "proxy_logs": ["请先上传图片或提供图片链接"]
        })

    # 检查image_source是否为有效的URL或base64数据
    is_valid = False
    if image_source.startswith('http://') or image_source.startswith('https://'):
        logger.info("图片源是URL格式")
        is_valid = True
    elif image_source.startswith('data:image/'):
        logger.info("图片源是base64格式")
        is_valid = True

    if not is_valid:
        logger.error(f"无效的图片源格式: {image_source[:50]}...")
        return jsonify({
            "success": False,
            "message": "图片格式不支持，请上传有效的图片",
            "proxy_logs": ["图片格式不支持，请上传有效的图片"]
        })

    try:
        # 同步执行视频生成
        result, message = generate_video(
            image_source, prompt, width, height, negative_prompt,
            motion_bucket_id, cond_aug, steps, seed, model_index
        )

        if result:
            # 生成成功，扣除积分
            success, points_message, new_points = points_system.process_generation(
                username, 'video', prompt, user['points']
            )

            if success:
                # 更新用户积分
                user_manager.update_user_points(username, -points_system.get_generation_cost('video'))
                # 添加生成记录，包含更多信息
                selected_model = MODELS[model_index] if model_index is not None else next((model for model in MODELS if model['type'] == 'video'), MODELS[-1])
                generation_history_manager.add_generation_record(
                    username=username,
                    generation_type='video',
                    prompt=prompt,
                    success=True,
                    negative_prompt=negative_prompt,
                    video_url=result["video_url"],
                    model_name=selected_model['display_name'],
                    width=width,
                    height=height,
                    steps=steps,
                    seed=seed,
                    motion_bucket_id=motion_bucket_id,
                    cond_aug=cond_aug,
                    generation_source='web'
                )

                # 更新用户生成计数
                user_manager.increment_generation_count(username)

                return jsonify({
                    "success": True,
                    "message": f"{message} {points_message}",
                    "video_url": result["video_url"],
                    "video_id": result["video_id"],
                    "proxy_logs": proxy_logs,
                    "user_points": new_points  # 返回更新后的积分
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"积分扣除失败: {points_message}",
                    "proxy_logs": proxy_logs
                })
        else:
            # 生成失败，记录失败记录但不扣积分
            selected_model = MODELS[model_index] if model_index is not None else next((model for model in MODELS if model['type'] == 'video'), MODELS[-1])
            generation_history_manager.add_generation_record(
                username=username,
                generation_type='video',
                prompt=prompt,
                success=False,
                negative_prompt=negative_prompt,
                model_name=selected_model['display_name'],
                width=width,
                height=height,
                steps=steps,
                seed=seed,
                motion_bucket_id=motion_bucket_id,
                cond_aug=cond_aug,
                generation_source='web'
            )
            return jsonify({
                "success": False,
                "message": message,
                "proxy_logs": proxy_logs
            })
    except Exception as e:
        logger.error(f"生成视频时发生错误: {e}")
        return jsonify({
            "success": False,
            "message": "视频生成失败，请稍后重试",
            "proxy_logs": ["视频生成失败，请稍后重试"]
        })

@app.route('/admin/users')
@login_required
def admin_users():
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    # 获取分页和搜索参数
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))  # 默认每页20条
    search = request.args.get('search', '').strip()

    # 获取所有用户信息（不包含密码）
    all_users = []
    for uname, udata in user_manager.users.items():
        user_info = {
            'username': uname,
            'email': udata.get('email', ''),
            'points': udata.get('points', 0),
            'created_at': udata.get('created_at', ''),
            'last_login': udata.get('last_login', ''),
            'total_generated': udata.get('total_generated', 0),
            'is_admin': udata.get('is_admin', False),
            'is_authorized': udata.get('is_authorized', False),
            'authorized_at': udata.get('authorized_at', ''),
            'authorized_by': udata.get('authorized_by', ''),
            'status': udata.get('status', 'approved')
        }
        all_users.append(user_info)

    # 搜索过滤
    if search:
        filtered_users = []
        search_lower = search.lower()
        for user_info in all_users:
            # 搜索用户名和邮箱
            if (search_lower in user_info['username'].lower() or
                search_lower in user_info['email'].lower()):
                filtered_users.append(user_info)
        all_users = filtered_users

    # 按用户名排序
    all_users.sort(key=lambda x: x['username'])

    # 计算分页信息
    total_users = len(all_users)
    total_pages = (total_users + per_page - 1) // per_page  # 向上取整

    # 确保页码在有效范围内
    if page < 1:
        page = 1
    elif page > total_pages and total_pages > 0:
        page = total_pages

    # 计算分页数据
    start_index = (page - 1) * per_page
    end_index = start_index + per_page
    users_page = all_users[start_index:end_index]

    return jsonify({
        'success': True,
        'users': users_page,
        'pagination': {
            'current_page': page,
            'per_page': per_page,
            'total_users': total_users,
            'total_pages': total_pages,
            'has_prev': page > 1,
            'has_next': page < total_pages,
            'prev_page': page - 1 if page > 1 else None,
            'next_page': page + 1 if page < total_pages else None
        },
        'search': search
    })

@app.route('/admin/add_points', methods=['POST'])
@login_required
def admin_add_points():
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()
    points_to_add = int(data.get('points', 0))
    reason = data.get('reason', '管理员充值')

    if not target_username or points_to_add <= 0:
        return jsonify({'success': False, 'message': '参数错误'})

    # 检查目标用户是否存在
    target_user = user_manager.get_user(target_username)
    if not target_user:
        return jsonify({'success': False, 'message': '目标用户不存在'})

    # 更新积分
    success, message = user_manager.update_user_points(target_username, points_to_add)
    if success:
        # 记录管理员操作
        points_system.admin_adjust_points(target_username, points_to_add, reason)
        return jsonify({'success': True, 'message': f'成功为用户 {target_username} 充值 {points_to_add} 积分'})
    else:
        return jsonify({'success': False, 'message': message})

@app.route('/admin/statistics')
@login_required
def admin_statistics():
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    # 获取系统统计信息
    system_stats = points_system.get_system_statistics()
    top_users = points_system.get_top_users_by_generation(10)
    recent_activities = points_system.get_recent_activities(20)

    # 用户总数
    total_users = len(user_manager.users)

    stats = {
        'total_users': total_users,
        'system_stats': system_stats,
        'top_users': top_users,
        'recent_activities': recent_activities
    }

    return jsonify({'success': True, 'statistics': stats})

@app.route('/admin/recalculate_statistics', methods=['POST'])
@login_required
def admin_recalculate_statistics():
    """重新计算系统统计数据"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        # 重新计算统计数据
        new_stats = points_system.recalculate_statistics()

        return jsonify({
            'success': True,
            'message': '统计数据重新计算完成',
            'statistics': new_stats
        })
    except Exception as e:
        logger.error(f"重新计算统计数据失败: {e}")
        return jsonify({'success': False, 'message': f'重新计算失败: {str(e)}'})

@app.route('/admin/settings', methods=['GET', 'POST'])
@login_required
def admin_settings():
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    if request.method == 'GET':
        # 获取当前设置
        settings = points_system.get_settings()
        return jsonify({
            'success': True,
            'settings': {
                'global_proxy_api_url': settings.get('global_proxy_api_url', ''),
                'image_generation_cost': settings.get('image_generation_cost', 1),
                'video_generation_cost': settings.get('video_generation_cost', 5),
                'daily_bonus': settings.get('daily_bonus', 100),
                'require_registration_approval': settings.get('require_registration_approval', False),
                'gallery_image_proxy_enabled': settings.get('gallery_image_proxy_enabled', False)
            }
        })

    elif request.method == 'POST':
        # 更新设置
        data = request.get_json()
        new_settings = {}

        # 更新全局代理池API地址
        if 'global_proxy_api_url' in data:
            new_settings['global_proxy_api_url'] = data['global_proxy_api_url'].strip()

        # 更新生成成本设置
        if 'image_generation_cost' in data:
            new_settings['image_generation_cost'] = int(data['image_generation_cost'])

        if 'video_generation_cost' in data:
            new_settings['video_generation_cost'] = int(data['video_generation_cost'])

        # 更新签到积分设置
        if 'daily_bonus' in data:
            new_settings['daily_bonus'] = int(data['daily_bonus'])

        # 更新注册审核设置
        if 'require_registration_approval' in data:
            new_settings['require_registration_approval'] = bool(data['require_registration_approval'])

        # 更新画廊图片代理设置
        if 'gallery_image_proxy_enabled' in data:
            new_settings['gallery_image_proxy_enabled'] = bool(data['gallery_image_proxy_enabled'])

        # 保存设置
        success = points_system.update_settings(new_settings)

        if success:
            return jsonify({'success': True, 'message': '设置更新成功'})
        else:
            return jsonify({'success': False, 'message': '设置更新失败'})

@app.route('/admin/pending_users')
@login_required
def admin_pending_users():
    """获取待审核用户列表"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    pending_users = user_manager.get_pending_users()
    return jsonify({'success': True, 'pending_users': pending_users})

@app.route('/admin/approve_user', methods=['POST'])
@login_required
def admin_approve_user():
    """管理员审核通过用户"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()

    if not target_username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    success, message = user_manager.approve_user(target_username, username)

    if success:
        # 记录管理员操作
        points_system.admin_adjust_points(target_username, 10, f"用户审核通过，由管理员 {username} 操作")

    return jsonify({'success': success, 'message': message})

@app.route('/admin/reject_user', methods=['POST'])
@login_required
def admin_reject_user():
    """管理员拒绝用户"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()
    reason = data.get('reason', '').strip()

    if not target_username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    success, message = user_manager.reject_user(target_username, username, reason)
    return jsonify({'success': success, 'message': message})

@app.route('/admin/set_user_authorized', methods=['POST'])
@login_required
def admin_set_user_authorized():
    """管理员设置用户授权状态"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()
    authorized = data.get('authorized', False)

    if not target_username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    success, message = user_manager.set_user_authorized(target_username, username, authorized)
    return jsonify({'success': success, 'message': message})

@app.route('/admin/authorized_users')
@login_required
def admin_authorized_users():
    """获取授权用户列表"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    authorized_users = user_manager.get_authorized_users()
    return jsonify({'success': True, 'authorized_users': authorized_users})

@app.route('/admin/reset_user_password', methods=['POST'])
@login_required
def admin_reset_user_password():
    """管理员重置用户密码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()
    new_password = data.get('new_password', '').strip()

    if not target_username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    if not new_password:
        return jsonify({'success': False, 'message': '新密码不能为空'})

    if len(new_password) < 6:
        return jsonify({'success': False, 'message': '密码至少需要6个字符'})

    # 防止管理员重置自己的密码（安全考虑）
    if target_username == username:
        return jsonify({'success': False, 'message': '不能重置自己的密码'})

    success, message = user_manager.reset_user_password(target_username, new_password, username)
    return jsonify({'success': success, 'message': message})

@app.route('/admin/delete_user', methods=['POST'])
@login_required
def admin_delete_user():
    """管理员删除用户"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    target_username = data.get('username', '').strip()

    if not target_username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    # 防止管理员删除自己的账户
    if target_username == username:
        return jsonify({'success': False, 'message': '不能删除自己的账户'})

    # 防止删除其他管理员账户
    target_user = user_manager.get_user(target_username)
    if target_user and target_user.get('is_admin', False):
        return jsonify({'success': False, 'message': '不能删除其他管理员账户'})

    success, message = user_manager.delete_user(target_username, username)
    return jsonify({'success': success, 'message': message})

@app.route('/static/images/<filename>')
def serve_image(filename):
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))

# ==================== 分享链接相关路由 ====================

@app.route('/api/create_share', methods=['POST'])
@login_required
def create_share():
    """创建分享链接"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        data = request.get_json()
        title = data.get('title', '').strip()
        images = data.get('images', [])
        videos = data.get('videos', [])
        expire_days = 7  # 固定7天过期
        require_key = True  # 固定需要秘钥

        # 合并图片和视频记录
        media_records = []

        # 添加图片记录
        for image in images:
            image['type'] = 'image'
            media_records.append(image)

        # 添加视频记录
        for video in videos:
            video['type'] = 'video'
            media_records.append(video)

        if not media_records:
            return jsonify({'success': False, 'message': '请选择要分享的图片或视频'})

        if len(media_records) > 50:
            return jsonify({'success': False, 'message': '单次分享媒体数量不能超过50个'})

        # 创建分享链接
        share_id, access_key = share_system.create_share(
            creator_username=username,
            media_records=media_records,
            expire_days=expire_days,
            require_key=require_key,
            title=title
        )

        if share_id:
            # 构建分享URL - 使用配置的域名
            share_url = f"{get_site_domain()}/share/{share_id}"

            return jsonify({
                'success': True,
                'message': '分享链接创建成功',
                'share_id': share_id,
                'share_url': share_url,
                'access_key': access_key
            })
        else:
            return jsonify({'success': False, 'message': '创建分享链接失败，请重试'})

    except Exception as e:
        logger.error(f"创建分享链接失败: {e}")
        return jsonify({'success': False, 'message': '创建分享链接失败，请重试'})

@app.route('/share/<share_id>')
def view_share(share_id):
    """查看分享内容"""
    # 获取访问秘钥（如果有）
    access_key = request.args.get('key', '').strip()

    # 检查用户是否已登录
    is_logged_in = 'username' in session
    current_user = None
    if is_logged_in:
        current_user = user_manager.get_user(session['username'])

    # 如果用户已登录，跳过秘钥验证
    if is_logged_in:
        # 直接获取分享数据，只验证分享是否存在和有效
        share_data = share_system.get_share(share_id)
        if not share_data:
            return render_template('share_error.html', message="分享链接不存在"), 404

        if not share_data['is_active']:
            return render_template('share_error.html', message="分享链接已失效"), 404

        # 检查是否过期
        try:
            from datetime import datetime
            expire_time = datetime.fromisoformat(share_data['expire_at'].replace('Z', '+00:00'))
            if datetime.now() > expire_time.replace(tzinfo=None):
                return render_template('share_error.html', message="分享链接已过期"), 404
        except (ValueError, KeyError):
            return render_template('share_error.html', message="分享链接数据异常"), 404

        # 记录访问
        share_system.record_view(share_id)

        return render_template('share_view.html',
                             share_data=share_data,
                             current_user=current_user)

    # 未登录用户需要验证秘钥
    success, message, share_data = share_system.verify_share_access(share_id, access_key)

    if not success:
        # 如果需要秘钥但未提供，显示秘钥输入页面
        if message == "需要访问秘钥":
            return render_template('share_key.html', share_id=share_id)
        else:
            # 其他错误，显示错误页面
            return render_template('share_error.html', message=message), 404

    # 记录访问
    share_system.record_view(share_id)

    return render_template('share_view.html',
                         share_data=share_data,
                         current_user=current_user)

@app.route('/api/verify_share_key', methods=['POST'])
def verify_share_key():
    """验证分享访问秘钥"""
    try:
        data = request.get_json()
        share_id = data.get('share_id', '').strip()
        access_key = data.get('access_key', '').strip()

        if not share_id or not access_key:
            return jsonify({'success': False, 'message': '请输入访问秘钥'})

        # 验证访问权限
        success, message, share_data = share_system.verify_share_access(share_id, access_key)

        if success:
            # 验证成功，返回重定向URL
            redirect_url = f"/share/{share_id}?key={access_key}"
            return jsonify({
                'success': True,
                'message': '验证成功',
                'redirect_url': redirect_url
            })
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        logger.error(f"验证分享秘钥失败: {e}")
        return jsonify({'success': False, 'message': '验证失败，请重试'})

@app.route('/api/my_shares')
@login_required
def my_shares():
    """获取我的分享列表"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        shares = share_system.get_user_shares(username, include_expired=True)

        # 为每个分享添加完整的URL - 使用配置的域名
        for share in shares:
            share['share_url'] = f"{get_site_domain()}/share/{share['id']}"

        return jsonify({
            'success': True,
            'shares': shares
        })
    except Exception as e:
        logger.error(f"获取分享列表失败: {e}")
        return jsonify({'success': False, 'message': '获取分享列表失败'})

@app.route('/api/delete_share', methods=['POST'])
@login_required
def delete_share():
    """删除分享链接"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        data = request.get_json()
        share_id = data.get('share_id', '').strip()

        if not share_id:
            return jsonify({'success': False, 'message': '分享ID不能为空'})

        success, message = share_system.delete_share(share_id, username)
        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"删除分享链接失败: {e}")
        return jsonify({'success': False, 'message': '删除失败，请重试'})

# ==================== 兑换码相关路由 ====================

@app.route('/redeem_code', methods=['POST'])
@login_required
def redeem_code():
    """用户使用兑换码"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    data = request.get_json()
    code = data.get('code', '').strip().upper()

    if not code:
        return jsonify({'success': False, 'message': '请输入兑换码'})

    # 使用兑换码
    success, message, points = redemption_system.use_redemption_code(code, username)

    if success:
        # 更新用户积分
        user_manager.update_user_points(username, points)
        # 记录积分交易
        points_system.add_transaction(username, points, 'redeem', f'兑换码: {code}')

        # 获取更新后的用户信息
        user = user_manager.get_user(username)
        return jsonify({
            'success': True,
            'message': message,
            'points_gained': points,
            'new_points': user['points']
        })
    else:
        return jsonify({'success': False, 'message': message})

@app.route('/daily_checkin', methods=['GET'])
@login_required
def daily_checkin():
    """每日签到"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    # 检查今日是否已签到
    user = user_manager.get_user(username)
    if not user:
        return jsonify({'success': False, 'message': '用户不存在'})

    # 获取今日日期（北京时间）
    from beijing_time import beijing_now, format_beijing_time
    today = format_beijing_time(beijing_now(), '%Y-%m-%d')

    # 检查用户签到记录
    last_checkin = user.get('last_checkin_date', '')

    if last_checkin == today:
        return jsonify({'success': False, 'message': '今日已签到，请明天再来！'})

    # 执行签到
    checkin_points = points_system.get_settings().get('daily_bonus', 100)  # 默认100积分

    # 更新用户积分
    success, message = user_manager.update_user_points(username, checkin_points)
    if not success:
        return jsonify({'success': False, 'message': '签到失败，请重试'})

    # 更新用户签到记录
    user['last_checkin_date'] = today
    user['total_checkins'] = user.get('total_checkins', 0) + 1
    user_manager.save_users()

    # 记录积分交易
    points_system.add_transaction(username, checkin_points, 'checkin', '每日签到奖励')

    # 获取更新后的用户信息
    updated_user = user_manager.get_user(username)

    return jsonify({
        'success': True,
        'message': f'签到成功！获得 {checkin_points} 积分',
        'points_gained': checkin_points,
        'new_points': updated_user['points'],
        'total_checkins': updated_user.get('total_checkins', 1)
    })

@app.route('/checkin_status', methods=['GET'])
@login_required
def checkin_status():
    """检查用户今日签到状态"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    user = user_manager.get_user(username)
    if not user:
        return jsonify({'success': False, 'message': '用户不存在'})

    # 获取今日日期（北京时间）
    from beijing_time import beijing_now, format_beijing_time
    today = format_beijing_time(beijing_now(), '%Y-%m-%d')

    # 检查用户签到记录
    last_checkin = user.get('last_checkin_date', '')
    has_checked_in = (last_checkin == today)

    return jsonify({
        'success': True,
        'has_checked_in': has_checked_in,
        'last_checkin_date': last_checkin,
        'total_checkins': user.get('total_checkins', 0)
    })

@app.route('/user/redemption_history')
@login_required
def user_redemption_history():
    """获取用户的兑换记录"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    records = redemption_system.get_user_usage_records(username)
    return jsonify({'success': True, 'records': records})

# ==================== 管理员兑换码管理路由 ====================

@app.route('/admin/redemption_codes')
@login_required
def admin_redemption_codes():
    """获取所有兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    codes = redemption_system.get_all_codes()
    return jsonify({'success': True, 'codes': codes})

@app.route('/admin/create_redemption_codes', methods=['POST'])
@login_required
def admin_create_redemption_codes():
    """管理员创建兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    code_type = data.get('type', 'one_time')  # 'one_time' 或 'activity'
    points = int(data.get('points', 10))
    count = int(data.get('count', 1))
    expire_days = int(data.get('expire_days', 30))
    description = data.get('description', '').strip()

    if points <= 0:
        return jsonify({'success': False, 'message': '积分数必须大于0'})

    if count <= 0 or count > 1000:
        return jsonify({'success': False, 'message': '生成数量必须在1-1000之间'})

    if expire_days <= 0 or expire_days > 365:
        return jsonify({'success': False, 'message': '过期天数必须在1-365之间'})

    try:
        codes = redemption_system.create_redemption_codes(
            code_type=code_type,
            points=points,
            count=count,
            expire_days=expire_days,
            description=description
        )

        return jsonify({
            'success': True,
            'message': f'成功生成 {len(codes)} 个兑换码',
            'codes': codes
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'生成兑换码失败: {str(e)}'})

@app.route('/admin/redemption_statistics')
@login_required
def admin_redemption_statistics():
    """获取兑换码统计信息"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    stats = redemption_system.get_statistics()
    return jsonify({'success': True, 'statistics': stats})

@app.route('/admin/redemption_usage_records')
@login_required
def admin_redemption_usage_records():
    """获取兑换码使用记录"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    records = redemption_system.get_usage_records()
    return jsonify({'success': True, 'records': records})

@app.route('/admin/toggle_redemption_code', methods=['POST'])
@login_required
def admin_toggle_redemption_code():
    """管理员启用/禁用兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    code = data.get('code', '').strip()
    action = data.get('action', '')  # 'activate' 或 'deactivate'

    if not code:
        return jsonify({'success': False, 'message': '兑换码不能为空'})

    if action == 'activate':
        success = redemption_system.activate_code(code)
        message = '兑换码已激活' if success else '激活失败'
    elif action == 'deactivate':
        success = redemption_system.deactivate_code(code)
        message = '兑换码已禁用' if success else '禁用失败'
    else:
        return jsonify({'success': False, 'message': '无效的操作'})

    return jsonify({'success': success, 'message': message})

@app.route('/admin/delete_redemption_code', methods=['POST'])
@login_required
def admin_delete_redemption_code():
    """管理员删除兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    code = data.get('code', '').strip()

    if not code:
        return jsonify({'success': False, 'message': '兑换码不能为空'})

    success = redemption_system.delete_code(code)
    message = '兑换码已删除' if success else '删除失败'

    return jsonify({'success': success, 'message': message})

@app.route('/admin/cleanup_disabled_codes', methods=['POST'])
@login_required
def admin_cleanup_disabled_codes():
    """管理员一键清理已禁用的兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        # 执行清理已禁用兑换码
        result = redemption_system.cleanup_disabled_codes()
        cleaned_count = result['cleaned_count']

        # 记录管理员操作
        logger.info(f"管理员 {username} 执行了已禁用兑换码清理: 清理了 {cleaned_count} 个已禁用兑换码")

        if cleaned_count > 0:
            message = f'清理完成！成功删除了 {cleaned_count} 个已禁用的兑换码'
        else:
            message = '没有找到需要清理的已禁用兑换码'

        return jsonify({
            'success': True,
            'message': message,
            'cleaned_count': cleaned_count,
            'cleaned_codes': result['cleaned_codes']
        })

    except Exception as e:
        logger.error(f"清理已禁用兑换码失败: {e}")
        return jsonify({'success': False, 'message': f'清理失败: {str(e)}'})

@app.route('/admin/cleanup_history', methods=['POST'])
@login_required
def admin_cleanup_history():
    """管理员清理用户历史记录"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        data = request.get_json()
        max_records = data.get('max_records', 50)
        days_to_keep = data.get('days_to_keep', 30)
        dry_run = data.get('dry_run', False)

        # 验证参数
        if not isinstance(max_records, int) or max_records < 1 or max_records > 1000:
            return jsonify({'success': False, 'message': '最大记录数必须在1-1000之间'})

        if not isinstance(days_to_keep, int) or days_to_keep < 1 or days_to_keep > 365:
            return jsonify({'success': False, 'message': '保留天数必须在1-365之间'})

        if dry_run:
            # 模拟清理，不实际修改数据
            stats = simulate_cleanup_stats(generation_history_manager, max_records, days_to_keep)
            return jsonify({
                'success': True,
                'message': '模拟清理完成',
                'statistics': stats,
                'dry_run': True
            })
        else:
            # 实际执行清理
            stats = generation_history_manager.cleanup_all_users_history(max_records, days_to_keep)

            # 记录管理员操作
            logger.info(f"管理员 {username} 执行了历史记录清理: 清理了 {stats['cleaned_users']} 个用户的 {stats['records_removed']} 条记录")

            return jsonify({
                'success': True,
                'message': f'清理完成！清理了 {stats["cleaned_users"]} 个用户的 {stats["records_removed"]} 条记录',
                'statistics': stats,
                'dry_run': False
            })

    except Exception as e:
        logger.error(f"清理历史记录失败: {e}")
        return jsonify({'success': False, 'message': f'清理失败: {str(e)}'})

@app.route('/admin/history_statistics')
@login_required
def admin_history_statistics():
    """获取历史记录统计信息"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        stats = get_history_statistics(generation_history_manager)
        return jsonify({'success': True, 'statistics': stats})
    except Exception as e:
        logger.error(f"获取历史记录统计失败: {e}")
        return jsonify({'success': False, 'message': f'获取统计失败: {str(e)}'})

@app.route('/admin/export_redemption_codes', methods=['POST'])
@login_required
def admin_export_redemption_codes():
    """管理员导出兑换码"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    data = request.get_json()
    codes = data.get('codes', [])

    if not codes:
        return jsonify({'success': False, 'message': '没有选择要导出的兑换码'})

    try:
        # 获取兑换码详细信息
        export_data = []
        for code in codes:
            code_info = redemption_system.get_code_info(code)
            if code_info:
                # 检查是否过期
                from beijing_time import is_beijing_time_after, beijing_now
                is_expired = is_beijing_time_after(beijing_now(), code_info['expire_at'])

                export_item = {
                    'code': code_info['code'],
                    'points': code_info['points'],
                    'type': code_info['type'],
                    'is_active': code_info['is_active'],
                    'is_expired': is_expired,
                    'created_at': code_info['created_at'],
                    'expire_at': code_info['expire_at'],
                    'description': code_info.get('description', ''),
                    'used_count': code_info['used_count']
                }
                export_data.append(export_item)

        return jsonify({
            'success': True,
            'message': f'成功获取 {len(export_data)} 个兑换码信息',
            'export_data': export_data
        })
    except Exception as e:
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})

# ==================== 申请管理相关路由 ====================

@app.route('/admin/applications')
@login_required
def admin_applications():
    """获取申请列表"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        # 获取筛选参数
        application_type = request.args.get('type', '')
        status = request.args.get('status', '')
        filter_username = request.args.get('username', '')
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))

        # 获取申请列表
        applications = application_system.get_applications(
            status=status if status else None,
            application_type=application_type if application_type else None,
            limit=limit,
            offset=offset
        )

        # 如果有用户名筛选，进一步过滤
        if filter_username:
            applications = [app for app in applications if filter_username.lower() in app['username'].lower()]

        return jsonify({
            'success': True,
            'applications': applications,
            'total': len(applications)
        })

    except Exception as e:
        logger.error(f"获取申请列表失败: {e}")
        return jsonify({'success': False, 'message': '获取申请列表失败'})

@app.route('/admin/applications/statistics')
@login_required
def admin_applications_statistics():
    """获取申请统计信息"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        statistics = application_system.get_statistics()
        return jsonify({
            'success': True,
            'statistics': statistics
        })

    except Exception as e:
        logger.error(f"获取申请统计失败: {e}")
        return jsonify({'success': False, 'message': '获取申请统计失败'})

@app.route('/admin/applications/<int:application_id>')
@login_required
def admin_application_detail(application_id):
    """获取申请详情"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        application = application_system.get_application_by_id(application_id)
        if not application:
            return jsonify({'success': False, 'message': '申请不存在'})

        return jsonify({
            'success': True,
            'application': application
        })

    except Exception as e:
        logger.error(f"获取申请详情失败: {e}")
        return jsonify({'success': False, 'message': '获取申请详情失败'})

@app.route('/admin/applications/<int:application_id>/process', methods=['POST'])
@login_required
def admin_process_application(application_id):
    """处理申请"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        data = request.get_json()
        action = data.get('action', '').strip()
        note = data.get('note', '').strip()

        if action not in ['approved', 'rejected']:
            return jsonify({'success': False, 'message': '无效的处理动作'})

        # 获取申请信息
        application = application_system.get_application_by_id(application_id)
        if not application:
            return jsonify({'success': False, 'message': '申请不存在'})

        # 处理申请
        success, message = application_system.process_application(
            application_id=application_id,
            status=action,
            admin_username=username,
            admin_note=note
        )

        if success and action == 'approved' and application['type'] == 'points':
            # 如果是点数申请且通过，给用户加积分
            points_amount = application.get('points_amount', 0)
            if points_amount > 0:
                user_success, user_message = user_manager.update_user_points(
                    application['username'],
                    points_amount
                )
                if user_success:
                    # 记录积分交易
                    points_system.add_transaction(
                        application['username'],
                        points_amount,
                        'admin_add',
                        f'申请通过 #{application_id}: {application.get("reason", "")[:50]}'
                    )
                    message += f"，已为用户 {application['username']} 添加 {points_amount} 积分"
                else:
                    logger.error(f"申请通过但积分添加失败: {user_message}")

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"处理申请失败: {e}")
        return jsonify({'success': False, 'message': '处理申请失败'})

# ==================== 站内信系统路由 ====================

@app.route('/messages')
@login_required
def messages_page():
    """站内信页面"""
    username = session.get('username')
    if not username:
        return redirect(url_for('index'))

    user = user_manager.get_user(username)
    if not user:
        return redirect(url_for('index'))

    # 检查是否是强制阅读模式
    force_read = request.args.get('force_read', '0') == '1'
    unread_count = message_system.get_unread_count(username)

    return render_template('messages.html',
                         current_user=user,
                         force_read=force_read,
                         unread_count=unread_count)

@app.route('/api/messages', methods=['GET'])
@login_required
def get_messages():
    """获取用户的站内信列表"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'

        messages = message_system.get_user_messages(username, limit, offset, unread_only)
        unread_count = message_system.get_unread_count(username)

        return jsonify({
            'success': True,
            'messages': messages,
            'unread_count': unread_count
        })

    except Exception as e:
        logger.error(f"获取消息失败: {e}")
        return jsonify({'success': False, 'message': '获取消息失败'})

@app.route('/api/messages/unread_count', methods=['GET'])
@login_required
def get_unread_count():
    """获取用户未读消息数量"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        unread_count = message_system.get_unread_count(username)
        return jsonify({
            'success': True,
            'unread_count': unread_count
        })

    except Exception as e:
        logger.error(f"获取未读消息数量失败: {e}")
        return jsonify({'success': False, 'message': '获取未读消息数量失败'})

@app.route('/api/messages/<message_id>/read', methods=['POST'])
@login_required
def mark_message_read(message_id):
    """标记消息为已读"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        success, message = message_system.mark_as_read(message_id, username)
        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"标记消息已读失败: {e}")
        return jsonify({'success': False, 'message': '操作失败'})

@app.route('/api/messages/read_all', methods=['POST'])
@login_required
def mark_all_messages_read():
    """标记所有消息为已读"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        success, message = message_system.mark_all_as_read(username)
        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"批量标记已读失败: {e}")
        return jsonify({'success': False, 'message': '操作失败'})

@app.route('/api/messages/<message_id>', methods=['GET'])
@login_required
def get_message_detail(message_id):
    """获取消息详情"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        message = message_system.get_message_by_id(message_id)
        if not message:
            return jsonify({'success': False, 'message': '消息不存在'})

        # 检查权限
        if message['recipient'] != username and message['recipient'] != 'all':
            return jsonify({'success': False, 'message': '无权限查看此消息'})

        return jsonify({'success': True, 'message': message})

    except Exception as e:
        logger.error(f"获取消息详情失败: {e}")
        return jsonify({'success': False, 'message': '获取消息详情失败'})

@app.route('/api/messages/<message_id>', methods=['DELETE'])
@login_required
def delete_message(message_id):
    """删除消息"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        success, message = message_system.delete_message(message_id, username)
        return jsonify({'success': success, 'message': message})

    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        return jsonify({'success': False, 'message': '删除失败'})

@app.route('/api/messages/check_can_return_home', methods=['GET'])
@login_required
def check_can_return_home():
    """检查用户是否可以返回主页（即是否还有未读消息）"""
    username = session.get('username')
    if not username:
        return jsonify({'success': False, 'message': '请先登录'})

    try:
        unread_count = message_system.get_unread_count(username)
        can_return = unread_count == 0

        return jsonify({
            'success': True,
            'can_return_home': can_return,
            'unread_count': unread_count
        })

    except Exception as e:
        logger.error(f"检查返回主页权限失败: {e}")
        return jsonify({'success': False, 'message': '检查失败'})

# ==================== 管理员站内信管理路由 ====================

@app.route('/admin/messages/send', methods=['POST'])
@login_required
def admin_send_message():
    """管理员发送站内信"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        data = request.get_json()
        recipient = data.get('recipient', '').strip()
        title = data.get('title', '').strip()
        content = data.get('content', '').strip()

        if not recipient or not title or not content:
            return jsonify({'success': False, 'message': '收件人、标题和内容不能为空'})

        # 验证收件人
        if recipient != 'all':
            target_user = user_manager.get_user(recipient)
            if not target_user:
                return jsonify({'success': False, 'message': '收件人不存在'})

        success, result = message_system.send_message(
            sender=username,
            recipient=recipient,
            title=title,
            content=content,
            message_type='admin'
        )

        if success:
            return jsonify({
                'success': True,
                'message': '站内信发送成功',
                'message_id': result
            })
        else:
            return jsonify({'success': False, 'message': result})

    except Exception as e:
        logger.error(f"管理员发送站内信失败: {e}")
        return jsonify({'success': False, 'message': '发送失败'})

@app.route('/admin/messages/statistics', methods=['GET'])
@login_required
def admin_message_statistics():
    """获取站内信统计信息"""
    username = session.get('username')
    user = user_manager.get_user(username)

    if not user or not user.get('is_admin', False):
        return jsonify({'success': False, 'message': '需要管理员权限'})

    try:
        stats = message_system.get_statistics()
        return jsonify({
            'success': True,
            'statistics': stats
        })

    except Exception as e:
        logger.error(f"获取站内信统计失败: {e}")
        return jsonify({'success': False, 'message': '获取统计信息失败'})

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # 初始化视频模型队列
    init_video_queues()

    logger.info("视频模型队列初始化完成")
    for model_key, queue_info in video_queues.items():
        logger.info(f"- {model_key}: {queue_info['model_name']}")

    # 启动Flask应用
    app.run(host='0.0.0.0', port=7799)
