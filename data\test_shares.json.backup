{"shares": {"E74uM85efrsj": {"id": "E74uM85efrsj", "creator": "testuser", "title": "测试分享", "images": [{"image_url": "https://example.com/image1.jpg", "prompt": "测试提示词1", "username": "testuser", "timestamp": "2025-01-01T12:00:00Z", "model_name": "测试模型"}, {"image_url": "https://example.com/image2.jpg", "prompt": "测试提示词2", "username": "testuser", "timestamp": "2025-01-01T12:30:00Z", "model_name": "测试模型"}], "access_key": "3ABGAYUV", "require_key": true, "created_at": "2025-07-25T14:07:17.160096+08:00", "expire_at": "2025-08-01T14:07:17.160096+08:00", "is_active": true, "view_count": 1, "last_viewed": "2025-07-25T14:07:17.161103+08:00"}, "AaS479CcckkE": {"id": "AaS479CcckkE", "creator": "testuser", "title": "公开分享", "images": [{"image_url": "https://example.com/image1.jpg", "prompt": "测试提示词1", "username": "testuser", "timestamp": "2025-01-01T12:00:00Z", "model_name": "测试模型"}], "access_key": null, "require_key": false, "created_at": "2025-07-25T14:07:17.163098+08:00", "expire_at": "2025-07-28T14:07:17.163098+08:00", "is_active": true, "view_count": 0, "last_viewed": null}}, "statistics": {"total_shares_created": 2, "total_views": 1, "active_shares": 2}}