{"shares": {"XpMQEDpZKhwt": {"id": "XpMQEDpZKhwt", "creator": "admin", "title": "admin的分享", "images": [{"image_url": "https://menyu-miaomiaoharemdogma11.hf.space/gradio_api/file=/tmp/gradio/323e3f97fda99e4e852f61c1ab3cbb3267b14e3ff97a226842d694fc8516715a/image.png", "prompt": "blush, smile, bangs, multiple_girls, skirt, long_sleeves, navel, school_uniform, jacket, closed_eyes, cowboy_shot, pleated_skirt, outdoors, sky, 3girls, black_skirt, yuri, tree, black_jacket, petals, ", "username": "user2", "timestamp": "2025-07-23 13:12:22", "model_name": "\n                    Miaomiao Harem vPred Dogma 1.1\n                "}], "access_key": "KEBGFE5J", "require_key": true, "created_at": "2025-07-25T14:09:05.686384+08:00", "expire_at": "2025-08-01T14:09:05.686384+08:00", "is_active": true, "view_count": 1, "last_viewed": "2025-07-25T14:09:18.116289+08:00"}, "HrwtwXmUNs39": {"id": "HrwtwXmUNs39", "creator": "admin", "title": "admin的分享", "images": [{"image_url": "https://menyu-miaomiaoharemdogma11.hf.space/gradio_api/file=/tmp/gradio/323e3f97fda99e4e852f61c1ab3cbb3267b14e3ff97a226842d694fc8516715a/image.png", "prompt": "blush, smile, bangs, multiple_girls, skirt, long_sleeves, navel, school_uniform, jacket, closed_eyes, cowboy_shot, pleated_skirt, outdoors, sky, 3girls, black_skirt, yuri, tree, black_jacket, petals, ", "username": "user2", "timestamp": "2025-07-23 13:12:22", "model_name": "\n                    Miaomiao Harem vPred Dogma 1.1\n                "}, {"image_url": "https://example.com/test-image-3.jpg", "prompt": "cyberpunk city, neon lights, futuristic, night scene, detailed architecture, sci-fi", "username": "admin", "timestamp": "2025-07-22 14:52:22", "model_name": "\n                    illustrious_pencil 融合\n                "}, {"image_url": "https://example.com/test-image-2.jpg", "prompt": "landscape, mountain, sunset, beautiful scenery, nature, peaceful, golden hour, detailed, masterpiece", "username": "admin", "timestamp": "2025-07-22 15:12:22", "model_name": "\n                    NoobAIXL VPred 0.65s\n                "}], "access_key": "YSNF48BL", "require_key": true, "created_at": "2025-07-25T14:09:44.518699+08:00", "expire_at": "2025-08-01T14:09:44.518699+08:00", "is_active": true, "view_count": 0, "last_viewed": null}, "ZCxCyJ6EygwW": {"id": "ZCxCyJ6EygwW", "creator": "admin", "title": "admin的分享", "images": [{"image_url": "https://menyu-miaomiaoharemdogma11.hf.space/gradio_api/file=/tmp/gradio/323e3f97fda99e4e852f61c1ab3cbb3267b14e3ff97a226842d694fc8516715a/image.png", "prompt": "blush, smile, bangs, multiple_girls, skirt, long_sleeves, navel, school_uniform, jacket, closed_eyes, cowboy_shot, pleated_skirt, outdoors, sky, 3girls, black_skirt, yuri, tree, black_jacket, petals, ", "username": "user2", "timestamp": "2025-07-23 13:12:22", "model_name": "\n                    Miaomiao Harem vPred Dogma 1.1\n                "}], "access_key": "6Y553YBC", "require_key": true, "created_at": "2025-07-25T14:20:11.504115+08:00", "expire_at": "2025-08-01T14:20:11.504115+08:00", "is_active": true, "view_count": 1, "last_viewed": "2025-07-25T14:23:58.643221+08:00"}, "kQQhW8CwmFGw": {"id": "kQQhW8CwmFGw", "creator": "api_test_user", "title": "API测试分享 - 图片和视频", "images": [{"type": "image", "image_url": "https://example.com/test_image1.jpg", "prompt": "API测试图片提示词", "username": "api_test_user", "timestamp": "2025-07-26T12:00:00", "model_name": "API测试图片模型"}], "videos": [{"type": "video", "video_url": "https://example.com/test_video1.mp4", "prompt": "API测试视频提示词", "username": "api_test_user", "timestamp": "2025-07-26T12:05:00", "model_name": "API测试视频模型"}], "access_key": "F6PNRFYC", "require_key": true, "created_at": "2025-07-26T13:45:17.235160+08:00", "expire_at": "2025-08-02T13:45:17.235160+08:00", "is_active": true, "view_count": 0, "last_viewed": null}, "bRC5NMWxrD6g": {"id": "bRC5NMWxrD6g", "creator": "api_test_user", "title": "API测试分享 - 图片和视频", "images": [{"type": "image", "image_url": "https://example.com/test_image1.jpg", "prompt": "API测试图片提示词", "username": "api_test_user", "timestamp": "2025-07-26T12:00:00", "model_name": "API测试图片模型"}], "videos": [{"type": "video", "video_url": "https://example.com/test_video1.mp4", "prompt": "API测试视频提示词", "username": "api_test_user", "timestamp": "2025-07-26T12:05:00", "model_name": "API测试视频模型"}], "access_key": "FYR9KLD9", "require_key": true, "created_at": "2025-07-26T13:45:17.241145+08:00", "expire_at": "2025-08-02T13:45:17.241145+08:00", "is_active": true, "view_count": 0, "last_viewed": null}}, "statistics": {"total_shares_created": 5, "total_views": 2, "active_shares": 5}}