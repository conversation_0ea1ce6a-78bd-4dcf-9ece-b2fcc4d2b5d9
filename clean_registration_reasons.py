#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理用户数据中的 registration_reason 字段
防止特殊字符破坏 JSON 格式
"""

import json
import os
import shutil
import re
from datetime import datetime


def sanitize_text_input(text):
    """清理文本输入，防止特殊字符破坏JSON格式"""
    if not text:
        return ''
    
    # 移除或替换可能破坏JSON的字符
    # 保留基本的标点符号，但移除控制字符和一些特殊字符
    
    # 移除控制字符（除了换行符和制表符）
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # 限制长度，防止过长的输入
    if len(text) > 500:
        text = text[:500]
    
    # 移除连续的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()


def clean_registration_reasons():
    """清理 users.json 中的 registration_reason 字段"""
    
    users_file = 'users.json'
    
    if not os.path.exists(users_file):
        print(f"错误: {users_file} 文件不存在")
        return False
    
    # 创建备份
    backup_file = f'users.json.backup_clean_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    try:
        shutil.copy2(users_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
    except Exception as e:
        print(f"创建备份失败: {e}")
        return False
    
    try:
        # 读取用户数据
        with open(users_file, 'r', encoding='utf-8') as f:
            users_data = json.load(f)
        
        print(f"成功读取用户数据，共 {len(users_data)} 个用户")
        
        # 统计需要清理的用户
        cleaned_count = 0
        total_users = len(users_data)
        
        # 清理每个用户的 registration_reason 字段
        for username, user_data in users_data.items():
            if 'registration_reason' in user_data:
                original_reason = user_data['registration_reason']
                cleaned_reason = sanitize_text_input(original_reason)
                
                if original_reason != cleaned_reason:
                    user_data['registration_reason'] = cleaned_reason
                    cleaned_count += 1
                    print(f"清理用户 {username} 的申请理由:")
                    print(f"  原始: {repr(original_reason[:100])}{'...' if len(original_reason) > 100 else ''}")
                    print(f"  清理后: {repr(cleaned_reason[:100])}{'...' if len(cleaned_reason) > 100 else ''}")
        
        # 保存清理后的数据
        with open(users_file, 'w', encoding='utf-8') as f:
            json.dump(users_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n清理完成!")
        print(f"总用户数: {total_users}")
        print(f"清理用户数: {cleaned_count}")
        print(f"未修改用户数: {total_users - cleaned_count}")
        
        # 验证JSON格式
        try:
            with open(users_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print("JSON格式验证通过!")
            return True
        except Exception as e:
            print(f"JSON格式验证失败: {e}")
            print("正在恢复备份文件...")
            shutil.copy2(backup_file, users_file)
            return False
            
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print("文件可能已经损坏，请使用其他修复工具")
        return False
    except Exception as e:
        print(f"清理过程中出现错误: {e}")
        return False


def preview_changes():
    """预览将要进行的清理操作，不实际修改文件"""
    
    users_file = 'users.json'
    
    if not os.path.exists(users_file):
        print(f"错误: {users_file} 文件不存在")
        return False
    
    try:
        # 读取用户数据
        with open(users_file, 'r', encoding='utf-8') as f:
            users_data = json.load(f)
        
        print(f"预览模式 - 共 {len(users_data)} 个用户")
        
        # 统计需要清理的用户
        need_cleaning = []
        
        # 检查每个用户的 registration_reason 字段
        for username, user_data in users_data.items():
            if 'registration_reason' in user_data:
                original_reason = user_data['registration_reason']
                cleaned_reason = sanitize_text_input(original_reason)
                
                if original_reason != cleaned_reason:
                    need_cleaning.append({
                        'username': username,
                        'original': original_reason,
                        'cleaned': cleaned_reason
                    })
        
        print(f"\n需要清理的用户数: {len(need_cleaning)}")
        
        if need_cleaning:
            print("\n前10个需要清理的示例:")
            for i, item in enumerate(need_cleaning[:10]):
                print(f"{i+1}. 用户: {item['username']}")
                print(f"   原始: {repr(item['original'][:100])}{'...' if len(item['original']) > 100 else ''}")
                print(f"   清理后: {repr(item['cleaned'][:100])}{'...' if len(item['cleaned']) > 100 else ''}")
                print()
        else:
            print("没有发现需要清理的数据")
        
        return True
        
    except Exception as e:
        print(f"预览过程中出现错误: {e}")
        return False


if __name__ == "__main__":
    print("用户数据清理工具")
    print("=" * 50)
    
    # 先预览
    print("1. 预览需要清理的数据...")
    if preview_changes():
        print("\n" + "=" * 50)
        
        # 询问是否继续
        response = input("是否继续执行清理操作？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("\n2. 执行清理操作...")
            if clean_registration_reasons():
                print("\n清理成功完成!")
            else:
                print("\n清理失败!")
        else:
            print("操作已取消")
    else:
        print("预览失败!")
