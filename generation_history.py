import json
import os
import threading
from datetime import datetime, timedelta

# 导入北京时间工具函数
from beijing_time import beijing_now_iso, beijing_now

class GenerationHistoryManager:
    """生成历史管理系统 - 独立管理所有用户的生成历史记录"""
    
    def __init__(self, history_file='generation_history.json'):
        self.history_file = history_file
        self.history_data = {}  # 格式: {username: [records...]}
        self.lock = threading.Lock()
        self.load_history()
    
    def load_history(self):
        """加载生成历史数据"""
        try:
            if os.path.exists(self.history_file):
                # 检查文件是否为空
                if os.path.getsize(self.history_file) == 0:
                    self.history_data = {}
                    print("生成历史文件为空，创建新的历史记录")
                else:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        self.history_data = json.load(f)
                    print(f"成功加载生成历史数据，包含 {len(self.history_data)} 个用户的记录")
            else:
                self.history_data = {}
                print("生成历史文件不存在，创建新的历史记录")
        except Exception as e:
            print(f"加载生成历史失败: {e}")
            self.history_data = {}
    
    def save_history(self):
        """保存生成历史数据"""
        try:
            # 创建备份
            if os.path.exists(self.history_file):
                backup_file = f"{self.history_file}.backup"
                import shutil
                shutil.copy2(self.history_file, backup_file)

            # 保存数据
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存生成历史失败: {e}")
            return False
    
    def add_generation_record(self, username, generation_type, prompt, success=True, max_records=50,
                             negative_prompt=None, image_url=None, video_url=None, model_name=None,
                             width=None, height=None, steps=None, cfg=None, seed=None,
                             enable_hr=None, restore_faces=None, motion_bucket_id=None,
                             cond_aug=None, generation_source='web'):
        """添加生成记录
        
        Args:
            username: 用户名
            generation_type: 生成类型 ('image' or 'video')
            prompt: 提示词
            success: 是否成功
            max_records: 最大保留记录数，默认50条
            negative_prompt: 负面提示词
            image_url: 图片URL
            video_url: 视频URL
            model_name: 使用的模型名称
            width: 图片宽度
            height: 图片高度
            steps: 生成步数
            cfg: CFG值
            seed: 随机种子
            enable_hr: 是否启用高分辨率
            restore_faces: 是否修复面部
            motion_bucket_id: 视频运动强度
            cond_aug: 视频条件增强
            generation_source: 生成来源 ('web', 'api')
        """
        with self.lock:
            # 确保用户存在于历史记录中
            if username not in self.history_data:
                self.history_data[username] = []
            
            record = {
                'type': generation_type,  # 'image' or 'video'
                'prompt': prompt[:2000],  # 限制提示词长度
                'negative_prompt': negative_prompt[:2000] if negative_prompt else '',
                'timestamp': beijing_now_iso(),
                'success': success,
                'username': username,
                'model_name': model_name or '',
                'image_url': image_url if generation_type == 'image' and success else None,
                'video_url': video_url if generation_type == 'video' and success else None,
                'generation_source': generation_source,
                # API参数信息
                'parameters': {
                    'width': width,
                    'height': height,
                    'steps': steps,
                    'cfg': cfg,
                    'seed': seed,
                    'enable_hr': enable_hr,
                    'restore_faces': restore_faces,
                    'motion_bucket_id': motion_bucket_id,
                    'cond_aug': cond_aug
                }
            }
            
            self.history_data[username].append(record)
            
            # 优化：只保留最近的指定条数记录，减少存储空间
            if len(self.history_data[username]) > max_records:
                self.history_data[username] = self.history_data[username][-max_records:]
            
            # 定期清理过期记录（超过30天的记录）
            self._cleanup_old_records(username, days_to_keep=30)
            
            # 保存数据
            self.save_history()
            return True
    
    def _cleanup_old_records(self, username, days_to_keep=30):
        """清理过期的生成记录
        
        Args:
            username: 用户名
            days_to_keep: 保留天数，默认30天
        """
        if username not in self.history_data or not self.history_data[username]:
            return
        
        cutoff_date = beijing_now() - timedelta(days=days_to_keep)
        
        # 过滤掉过期的记录
        filtered_history = []
        for record in self.history_data[username]:
            try:
                # 解析时间戳
                record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                # 转换为北京时间进行比较
                if record_time.tzinfo is not None:
                    record_time = record_time.astimezone(beijing_now().tzinfo)
                else:
                    # 如果没有时区信息，假设为北京时间
                    record_time = record_time.replace(tzinfo=beijing_now().tzinfo)

                if record_time > cutoff_date:
                    filtered_history.append(record)
            except (ValueError, KeyError):
                # 如果时间戳格式有问题，保留记录以避免数据丢失
                filtered_history.append(record)
        
        self.history_data[username] = filtered_history
    
    def get_user_history(self, username, limit=50):
        """获取用户的生成历史记录
        
        Args:
            username: 用户名
            limit: 返回记录数量限制
            
        Returns:
            list: 用户的生成历史记录列表
        """
        if username not in self.history_data:
            return []
        
        records = self.history_data[username]
        # 按时间倒序返回最近的记录
        return sorted(records, key=lambda x: x['timestamp'], reverse=True)[:limit]
    
    def get_recent_gallery_records(self, hours=1, limit=50, search=''):
        """获取最近一段时间内所有用户的成功生成记录，用于画廊展示
        
        Args:
            hours: 获取多少小时内的记录，默认1小时
            limit: 最大返回记录数，默认50条
            search: 搜索关键词，在提示词、用户名中搜索
            
        Returns:
            list: 按时间倒序排列的生成记录列表
        """
        # 计算时间范围 - 使用北京时间
        cutoff_time = beijing_now() - timedelta(hours=hours)
        gallery_records = []
        
        # 遍历所有用户的生成历史
        for username, records in self.history_data.items():
            for record in records:
                # 只显示成功的记录
                if not record.get('success', False):
                    continue
                
                # 只显示有URL的记录
                if not (record.get('image_url') or record.get('video_url')):
                    continue
                
                # 检查时间范围
                try:
                    record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                    # 转换为北京时间进行比较
                    if record_time.tzinfo is not None:
                        record_time = record_time.astimezone(beijing_now().tzinfo)
                    else:
                        # 如果没有时区信息，假设为北京时间
                        record_time = record_time.replace(tzinfo=beijing_now().tzinfo)

                    if record_time < cutoff_time:
                        continue
                    
                    # 搜索过滤
                    if search:
                        search_lower = search.lower()
                        if not (search_lower in record.get('prompt', '').lower() or
                               search_lower in record.get('username', '').lower()):
                            continue
                    
                    gallery_records.append(record)
                    
                except (ValueError, KeyError):
                    # 时间戳格式有问题，跳过这条记录
                    continue
        
        # 按时间倒序排列
        gallery_records.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # 限制返回数量，最大504张（能被3整除，便于画廊分页显示）
        max_gallery_limit = 504
        actual_limit = min(limit, max_gallery_limit)
        return gallery_records[:actual_limit]
    
    def get_user_gallery_records(self, username, hours=1, limit=50, search=''):
        """获取特定用户最近一段时间内的成功生成记录，用于未授权用户的画廊展示
        
        Args:
            username: 用户名
            hours: 获取多少小时内的记录，默认1小时
            limit: 最大返回记录数，默认50条
            search: 搜索关键词，在提示词中搜索
            
        Returns:
            list: 按时间倒序排列的生成记录列表
        """
        if username not in self.history_data:
            return []
        
        # 计算时间范围 - 使用北京时间
        cutoff_time = beijing_now() - timedelta(hours=hours)
        gallery_records = []
        
        for record in self.history_data[username]:
            # 只显示成功的记录
            if not record.get('success', False):
                continue
            
            # 只显示有URL的记录
            if not (record.get('image_url') or record.get('video_url')):
                continue
            
            # 检查时间范围
            try:
                record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                # 转换为北京时间进行比较
                if record_time.tzinfo is not None:
                    record_time = record_time.astimezone(beijing_now().tzinfo)
                else:
                    # 如果没有时区信息，假设为北京时间
                    record_time = record_time.replace(tzinfo=beijing_now().tzinfo)

                if record_time < cutoff_time:
                    continue
                
                # 搜索过滤
                if search:
                    search_lower = search.lower()
                    if search_lower not in record.get('prompt', '').lower():
                        continue
                
                gallery_records.append(record)
                
            except (ValueError, KeyError):
                # 时间戳格式有问题，跳过这条记录
                continue
        
        # 按时间倒序排列
        gallery_records.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # 限制返回数量，最大504张（能被3整除，便于画廊分页显示）
        max_gallery_limit = 504
        actual_limit = min(limit, max_gallery_limit)
        return gallery_records[:actual_limit]
    
    def cleanup_all_users_history(self, max_records=50, days_to_keep=30):
        """批量清理所有用户的历史记录
        
        Args:
            max_records: 每个用户最大保留记录数
            days_to_keep: 保留天数
            
        Returns:
            dict: 清理统计信息
        """
        cleaned_users = 0
        total_records_before = 0
        total_records_after = 0
        
        with self.lock:
            for username in list(self.history_data.keys()):
                if username in self.history_data:
                    records_before = len(self.history_data[username])
                    total_records_before += records_before
                    
                    # 清理过期记录
                    self._cleanup_old_records(username, days_to_keep)
                    
                    # 限制记录数量
                    if len(self.history_data[username]) > max_records:
                        self.history_data[username] = self.history_data[username][-max_records:]
                    
                    records_after = len(self.history_data[username])
                    total_records_after += records_after
                    
                    if records_before > records_after:
                        cleaned_users += 1
            
            # 保存清理后的数据
            if cleaned_users > 0:
                self.save_history()
        
        return {
            'cleaned_users': cleaned_users,
            'total_records_before': total_records_before,
            'total_records_after': total_records_after,
            'records_removed': total_records_before - total_records_after
        }
    
    def get_user_stats(self, username):
        """获取用户的生成统计信息
        
        Args:
            username: 用户名
            
        Returns:
            dict: 用户统计信息
        """
        if username not in self.history_data:
            return {
                'total_generated': 0,
                'recent_history': []
            }
        
        records = self.history_data[username]
        successful_records = [r for r in records if r.get('success', False)]
        recent_history = sorted(records, key=lambda x: x['timestamp'], reverse=True)[:10]
        
        return {
            'total_generated': len(successful_records),
            'recent_history': recent_history
        }
    
    def delete_user_history(self, username):
        """删除用户的所有生成历史记录
        
        Args:
            username: 用户名
            
        Returns:
            bool: 是否删除成功
        """
        with self.lock:
            if username in self.history_data:
                del self.history_data[username]
                return self.save_history()
            return True
    
    def get_history_statistics(self):
        """获取历史记录统计信息"""
        total_users = len(self.history_data)
        users_with_history = 0
        total_records = 0
        max_records_per_user = 0
        min_records_per_user = float('inf')
        
        # 按时间段统计 - 使用北京时间
        now = beijing_now()
        records_last_7_days = 0
        records_last_30_days = 0
        
        user_record_counts = []
        
        for username, records in self.history_data.items():
            record_count = len(records)
            
            if record_count > 0:
                users_with_history += 1
                total_records += record_count
                max_records_per_user = max(max_records_per_user, record_count)
                min_records_per_user = min(min_records_per_user, record_count)
                user_record_counts.append(record_count)
                
                # 统计最近的记录
                for record in records:
                    try:
                        record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                        # 转换为北京时间进行比较
                        if record_time.tzinfo is not None:
                            record_time = record_time.astimezone(beijing_now().tzinfo)
                        else:
                            # 如果没有时区信息，假设为北京时间
                            record_time = record_time.replace(tzinfo=beijing_now().tzinfo)

                        if (now - record_time).days <= 7:
                            records_last_7_days += 1
                        if (now - record_time).days <= 30:
                            records_last_30_days += 1
                    except (ValueError, KeyError):
                        continue
        
        # 计算平均值
        avg_records_per_user = total_records / users_with_history if users_with_history > 0 else 0
        
        # 计算中位数
        if user_record_counts:
            user_record_counts.sort()
            n = len(user_record_counts)
            median_records = user_record_counts[n//2] if n % 2 == 1 else (user_record_counts[n//2-1] + user_record_counts[n//2]) / 2
        else:
            median_records = 0
        
        if min_records_per_user == float('inf'):
            min_records_per_user = 0
        
        return {
            'total_users': total_users,
            'users_with_history': users_with_history,
            'users_without_history': total_users - users_with_history,
            'total_records': total_records,
            'avg_records_per_user': round(avg_records_per_user, 2),
            'median_records_per_user': median_records,
            'max_records_per_user': max_records_per_user,
            'min_records_per_user': min_records_per_user,
            'records_last_7_days': records_last_7_days,
            'records_last_30_days': records_last_30_days
        }
