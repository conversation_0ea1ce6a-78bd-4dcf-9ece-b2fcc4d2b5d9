#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新版本视频API的脚本
"""

import requests
import json
import time

def test_new_video_api():
    """测试新版本的视频API"""

    # 使用一个简单的测试图片URL
    test_image_url = "https://picsum.photos/704/1280"

    # 构建请求数据
    image_data = {
        "path": test_image_url,
        "meta": {
            "_type": "gradio.FileData"
        }
    }
    
    # 根据你提供的测试例子，参数顺序应该是：
    # 图片参数, 提示词, 分辨率, 时长, 采样步数, Scale, Sample Shift, Seed
    data = {
        "data": [
            image_data,
            "A beautiful waterfall in a lush jungle, cinematic.",
            "704*1280",
            3,  # 时长默认3
            30,  # 采样步数默认30
            5,   # Scale默认5
            5,   # Sample Shift默认5
            -1   # Seed默认-1随机
        ]
    }
    
    # API地址
    url = "https://ginigen-wan-2-2-5b.hf.space/gradio_api/call/generate_video"
    
    headers = {"Content-Type": "application/json"}
    
    print("正在测试新版本视频API...")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, data=json.dumps(data), timeout=60)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_json = response.json()
            event_id = response_json.get("event_id")
            print(f"获取到事件ID: {event_id}")
            
            if event_id:
                # 获取结果
                result_url = f"https://ginigen-wan-2-2-5b.hf.space/gradio_api/call/generate_video/{event_id}"
                print(f"结果URL: {result_url}")
                
                # 等待一段时间
                print("等待40秒后开始检查结果...")
                time.sleep(40)
                
                # 检查结果
                for i in range(30):  # 最多检查30次
                    print(f"检查结果 ({i+1}/30)...")
                    result_response = requests.get(result_url, timeout=30)
                    result_text = result_response.text
                    
                    if "event: complete" in result_text:
                        print("生成完成！")
                        print("响应内容:")
                        print(result_text[:500] + "..." if len(result_text) > 500 else result_text)
                        break
                    elif "event: error" in result_text:
                        print("生成出错！")
                        print("错误响应:")
                        print(result_text[:500] + "..." if len(result_text) > 500 else result_text)
                        break
                    else:
                        print(f"仍在处理中... (第{i+1}次检查)")
                        time.sleep(6)
            else:
                print("未获取到事件ID")
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_new_video_api()
