<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新视频模型界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>新视频模型界面测试</h1>
        
        <div class="card">
            <div class="card-header">
                模型选择
            </div>
            <div class="card-body">
                <select id="modelSelect" class="form-select">
                    <option value="8" data-type="video" data-api-version="v1">视频生成模型主要服务器 wan2.1-fast</option>
                    <option value="12" data-type="video" data-api-version="v2">视频生成模型服务器5 wan2.2-5b</option>
                </select>
            </div>
        </div>

        <!-- 视频参数 -->
        <div id="videoParams" class="card mt-3">
            <div class="card-header">
                视频参数
            </div>
            <div class="card-body">
                <!-- 原版本API参数 (wan2.1-fast) -->
                <div id="videoParamsV1" class="row mb-3">
                    <div class="col-6">
                        <label for="motionBucketInput" class="form-label">运动强度:</label>
                        <input type="number" id="motionBucketInput" class="form-control" value="2" min="1" max="10">
                    </div>
                    <div class="col-6">
                        <label for="condAugInput" class="form-label">条件增强:</label>
                        <input type="number" id="condAugInput" class="form-control" value="1" min="0" max="5">
                    </div>
                </div>

                <!-- 新版本API参数 (wan2.2-5b) -->
                <div id="videoParamsV2" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-6">
                            <label for="resolutionSelect" class="form-label">分辨率:</label>
                            <select id="resolutionSelect" class="form-select">
                                <option value="704*1280">704×1280 (竖屏)</option>
                                <option value="1280*704">1280×704 (横屏)</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label for="durationInput" class="form-label">时长(秒):</label>
                            <input type="number" id="durationInput" class="form-control" value="3" min="1" max="10">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-4">
                            <label for="scaleInput" class="form-label">Scale:</label>
                            <input type="number" id="scaleInput" class="form-control" value="5" min="1" max="20">
                        </div>
                        <div class="col-4">
                            <label for="sampleShiftInput" class="form-label">Sample Shift:</label>
                            <input type="number" id="sampleShiftInput" class="form-control" value="5" min="1" max="20">
                        </div>
                        <div class="col-4">
                            <label for="videoSeedInput" class="form-label">种子:</label>
                            <input type="number" id="videoSeedInput" class="form-control" value="-1" min="-1" max="2147483647">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                当前参数状态
            </div>
            <div class="card-body">
                <div id="paramStatus">请选择模型</div>
            </div>
        </div>
    </div>

    <script>
        const modelSelect = document.getElementById('modelSelect');
        const videoParamsV1 = document.getElementById('videoParamsV1');
        const videoParamsV2 = document.getElementById('videoParamsV2');
        const paramStatus = document.getElementById('paramStatus');

        modelSelect.addEventListener('change', function() {
            const selectedOption = modelSelect.options[modelSelect.selectedIndex];
            const apiVersion = selectedOption.dataset.apiVersion;
            const modelName = selectedOption.textContent;

            if (apiVersion === 'v2') {
                videoParamsV1.style.display = 'none';
                videoParamsV2.style.display = 'block';
                paramStatus.innerHTML = `<strong>新版本API (v2)</strong><br>模型: ${modelName}<br>显示参数: 分辨率、时长、Scale、Sample Shift、种子`;
            } else {
                videoParamsV1.style.display = 'block';
                videoParamsV2.style.display = 'none';
                paramStatus.innerHTML = `<strong>原版本API (v1)</strong><br>模型: ${modelName}<br>显示参数: 运动强度、条件增强`;
            }
        });

        // 初始化
        modelSelect.dispatchEvent(new Event('change'));
    </script>
</body>
</html>
