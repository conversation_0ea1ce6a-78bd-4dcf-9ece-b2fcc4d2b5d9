<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站内信 - 梦羽AI绘图工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .message-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .message-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .message-item.unread {
            border-left: 4px solid #007bff;
            background: rgba(0, 123, 255, 0.05);
        }
        
        .message-header {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 10px 10px 0 0;
        }
        
        .message-content {
            padding: 1rem;
        }
        
        .message-actions {
            padding: 0.5rem 1rem;
            border-top: 1px solid #e9ecef;
            background: rgba(248, 249, 250, 0.5);
            border-radius: 0 0 10px 10px;
        }
        
        .message-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .btn-sm {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 2rem;
        }
        
        .no-messages {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .filter-tabs {
            margin-bottom: 1.5rem;
        }
        
        .stats-card {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 强制阅读提示 -->
        {% if force_read and unread_count > 0 %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert" style="border-radius: 15px; border: 2px solid #ffc107; background: linear-gradient(45deg, #fff3cd, #ffeaa7); box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                        <div class="flex-grow-1">
                            <h5 class="alert-heading mb-2">
                                <i class="fas fa-bell me-2"></i>重要提醒：您有 <span class="badge bg-danger">{{ unread_count }}</span> 条未读站内信
                            </h5>
                            <p class="mb-2">
                                <strong>为了确保您不错过重要信息，请先阅读所有未读消息后才能返回主页。</strong>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                点击下方的"<strong>全部标记已读</strong>"按钮或逐一查看消息来完成阅读。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 页面标题和导航 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0">
                                    <i class="fas fa-envelope me-2"></i>站内信
                                </h4>
                                <small class="text-muted">欢迎，{{ current_user.username }}！</small>
                            </div>
                            <div>
                                <button id="returnHomeBtn" class="btn btn-outline-primary btn-sm" onclick="checkAndReturnHome()">
                                    <i class="fas fa-home me-1"></i>返回首页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h5 id="totalMessages">0</h5>
                            <small>总消息数</small>
                        </div>
                        <div class="col-md-4">
                            <h5 id="unreadMessages">0</h5>
                            <small>未读消息</small>
                        </div>
                        <div class="col-md-4">
                            <h5 id="readMessages">0</h5>
                            <small>已读消息</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和操作按钮 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                            <div class="filter-tabs">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="messageFilter" id="filterAll" value="all" checked>
                                    <label class="btn btn-outline-primary" for="filterAll">
                                        <i class="fas fa-list me-1"></i>全部消息
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="messageFilter" id="filterUnread" value="unread">
                                    <label class="btn btn-outline-warning" for="filterUnread">
                                        <i class="fas fa-envelope me-1"></i>未读消息
                                    </label>
                                </div>
                            </div>
                            <div class="action-buttons">
                                <button id="markAllReadBtn" class="btn btn-outline-success btn-sm me-2">
                                    <i class="fas fa-check-double me-1"></i>全部标记已读
                                </button>
                                <button id="refreshBtn" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sync me-1"></i>刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <!-- 加载状态 -->
                        <div id="loadingSpinner" class="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载消息...</p>
                        </div>

                        <!-- 消息列表容器 -->
                        <div id="messagesList" class="d-none">
                            <!-- 消息项将通过JavaScript动态添加 -->
                        </div>

                        <!-- 无消息提示 -->
                        <div id="noMessages" class="no-messages d-none">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <h5>暂无消息</h5>
                            <p class="text-muted">您还没有收到任何站内信</p>
                        </div>

                        <!-- 分页 -->
                        <div id="paginationContainer" class="pagination-container d-none">
                            <nav>
                                <ul class="pagination" id="pagination">
                                    <!-- 分页按钮将通过JavaScript动态添加 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息详情模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">消息详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- 消息详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="markReadBtn">标记已读</button>
                    <button type="button" class="btn btn-danger" id="deleteMessageBtn">删除消息</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 全局变量
            let currentPage = 1;
            let currentFilter = 'all';
            let currentMessageId = null;
            const messagesPerPage = 10;

            // 元素引用
            const loadingSpinner = document.getElementById('loadingSpinner');
            const messagesList = document.getElementById('messagesList');
            const noMessages = document.getElementById('noMessages');
            const paginationContainer = document.getElementById('paginationContainer');
            const messageModal = new bootstrap.Modal(document.getElementById('messageModal'));

            // 初始化
            loadMessages();

            // 事件监听器
            document.querySelectorAll('input[name="messageFilter"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    currentFilter = this.value;
                    currentPage = 1;
                    loadMessages();
                });
            });

            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadMessages();
            });

            document.getElementById('markAllReadBtn').addEventListener('click', function() {
                markAllAsRead();
            });

            // 加载消息列表
            function loadMessages() {
                showLoading();
                
                const params = new URLSearchParams({
                    limit: messagesPerPage,
                    offset: (currentPage - 1) * messagesPerPage,
                    unread_only: currentFilter === 'unread'
                });

                fetch(`/api/messages?${params}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayMessages(data.messages);
                            updateStats(data.messages.length, data.unread_count);
                        } else {
                            showError('加载消息失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('加载消息出错:', error);
                        showError('加载消息失败，请稍后重试');
                    })
                    .finally(() => {
                        hideLoading();
                    });
            }

            // 显示消息列表
            function displayMessages(messages) {
                if (messages.length === 0) {
                    showNoMessages();
                    return;
                }

                let html = '';
                messages.forEach(message => {
                    const isUnread = !message.is_read;
                    const messageTypeClass = getMessageTypeClass(message.type);
                    const messageDate = new Date(message.timestamp).toLocaleString('zh-CN');

                    html += `
                        <div class="message-item ${isUnread ? 'unread' : ''}" data-message-id="${message.id}">
                            <div class="message-header">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">${escapeHtml(message.title)}</h6>
                                        <div class="d-flex align-items-center">
                                            <small class="text-muted me-2">
                                                <i class="fas fa-user me-1"></i>发送者: ${escapeHtml(message.sender)}
                                            </small>
                                            <span class="badge message-type-badge ${messageTypeClass}">
                                                ${getMessageTypeText(message.type)}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">${messageDate}</small>
                                        ${isUnread ? '<span class="badge bg-primary ms-2">未读</span>' : ''}
                                    </div>
                                </div>
                            </div>
                            <div class="message-content">
                                <p class="mb-0">${escapeHtml(message.content).substring(0, 100)}${message.content.length > 100 ? '...' : ''}</p>
                            </div>
                            <div class="message-actions">
                                <div class="d-flex justify-content-end">
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="viewMessage('${message.id}')">
                                        <i class="fas fa-eye me-1"></i>查看
                                    </button>
                                    ${isUnread ? `<button class="btn btn-outline-success btn-sm me-2" onclick="markAsRead('${message.id}')">
                                        <i class="fas fa-check me-1"></i>标记已读
                                    </button>` : ''}
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteMessage('${message.id}')">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                messagesList.innerHTML = html;
                messagesList.classList.remove('d-none');
                noMessages.classList.add('d-none');
            }

            // 显示加载状态
            function showLoading() {
                loadingSpinner.classList.remove('d-none');
                messagesList.classList.add('d-none');
                noMessages.classList.add('d-none');
            }

            // 隐藏加载状态
            function hideLoading() {
                loadingSpinner.classList.add('d-none');
            }

            // 显示无消息状态
            function showNoMessages() {
                noMessages.classList.remove('d-none');
                messagesList.classList.add('d-none');
            }

            // 显示错误信息
            function showError(message) {
                alert('错误: ' + message);
            }

            // 更新统计信息
            function updateStats(totalCount, unreadCount) {
                document.getElementById('totalMessages').textContent = totalCount;
                document.getElementById('unreadMessages').textContent = unreadCount;
                document.getElementById('readMessages').textContent = totalCount - unreadCount;
            }

            // 获取消息类型样式类
            function getMessageTypeClass(type) {
                switch(type) {
                    case 'admin': return 'bg-warning';
                    case 'system': return 'bg-info';
                    default: return 'bg-secondary';
                }
            }

            // 获取消息类型文本
            function getMessageTypeText(type) {
                switch(type) {
                    case 'admin': return '管理员';
                    case 'system': return '系统';
                    default: return '用户';
                }
            }

            // HTML转义
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 全局函数 - 查看消息
            window.viewMessage = function(messageId) {
                // 这里可以实现查看消息详情的功能
                fetch(`/api/messages/${messageId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessageModal(data.message);
                            if (!data.message.is_read) {
                                markAsRead(messageId);
                            }
                        } else {
                            showError('获取消息详情失败');
                        }
                    })
                    .catch(error => {
                        console.error('获取消息详情出错:', error);
                        showError('获取消息详情失败');
                    });
            };

            // 全局函数 - 标记为已读
            window.markAsRead = function(messageId) {
                fetch(`/api/messages/${messageId}/read`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadMessages(); // 重新加载消息列表
                        // 如果是强制阅读模式，检查是否可以返回主页
                        checkForceReadStatus();
                    } else {
                        showError('标记已读失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('标记已读出错:', error);
                    showError('标记已读失败');
                });
            };

            // 全局函数 - 删除消息
            window.deleteMessage = function(messageId) {
                if (confirm('确定要删除这条消息吗？')) {
                    fetch(`/api/messages/${messageId}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            loadMessages(); // 重新加载消息列表
                        } else {
                            showError('删除消息失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除消息出错:', error);
                        showError('删除消息失败');
                    });
                }
            };

            // 标记所有消息为已读
            function markAllAsRead() {
                if (confirm('确定要将所有消息标记为已读吗？')) {
                    fetch('/api/messages/read_all', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            loadMessages(); // 重新加载消息列表
                            alert(data.message);
                            // 如果是强制阅读模式，检查是否可以返回主页
                            checkForceReadStatus();
                        } else {
                            showError('批量标记已读失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('批量标记已读出错:', error);
                        showError('批量标记已读失败');
                    });
                }
            }

            // 显示消息详情模态框
            function showMessageModal(message) {
                currentMessageId = message.id;
                document.getElementById('messageModalTitle').textContent = message.title;

                const modalBody = document.getElementById('messageModalBody');
                modalBody.innerHTML = `
                    <div class="mb-3">
                        <strong>发送者:</strong> ${escapeHtml(message.sender)}
                    </div>
                    <div class="mb-3">
                        <strong>发送时间:</strong> ${new Date(message.timestamp).toLocaleString('zh-CN')}
                    </div>
                    <div class="mb-3">
                        <strong>消息类型:</strong>
                        <span class="badge ${getMessageTypeClass(message.type)}">${getMessageTypeText(message.type)}</span>
                    </div>
                    <div class="mb-3">
                        <strong>消息内容:</strong>
                        <div class="mt-2 p-3 bg-light rounded">
                            ${escapeHtml(message.content).replace(/\n/g, '<br>')}
                        </div>
                    </div>
                `;

                // 更新按钮状态
                const markReadBtn = document.getElementById('markReadBtn');
                if (message.is_read) {
                    markReadBtn.style.display = 'none';
                } else {
                    markReadBtn.style.display = 'inline-block';
                    markReadBtn.onclick = () => {
                        markAsRead(message.id);
                        messageModal.hide();
                    };
                }

                document.getElementById('deleteMessageBtn').onclick = () => {
                    deleteMessage(message.id);
                    messageModal.hide();
                };

                messageModal.show();
            }

            // 检查强制阅读状态
            function checkForceReadStatus() {
                const urlParams = new URLSearchParams(window.location.search);
                const forceRead = urlParams.get('force_read') === '1';

                if (forceRead) {
                    fetch('/api/messages/check_can_return_home')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.can_return_home) {
                                // 隐藏强制阅读提示
                                const alertElement = document.querySelector('.alert-warning');
                                if (alertElement) {
                                    alertElement.style.display = 'none';
                                }
                                // 显示成功提示
                                showSuccess('所有消息已阅读完毕，现在可以返回主页了！');
                            }
                        })
                        .catch(error => {
                            console.error('检查返回主页状态失败:', error);
                        });
                }
            }

            // 检查并返回主页
            window.checkAndReturnHome = function() {
                const urlParams = new URLSearchParams(window.location.search);
                const forceRead = urlParams.get('force_read') === '1';

                if (!forceRead) {
                    // 非强制阅读模式，直接返回
                    window.location.href = '/';
                    return;
                }

                // 强制阅读模式，检查是否还有未读消息
                fetch('/api/messages/check_can_return_home')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            if (data.can_return_home) {
                                window.location.href = '/';
                            } else {
                                alert(`您还有 ${data.unread_count} 条未读消息，请先阅读完所有消息后再返回主页。\n\n提示：您可以点击"全部标记已读"按钮快速完成阅读。`);
                            }
                        } else {
                            showError('检查返回权限失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('检查返回主页权限失败:', error);
                        showError('检查返回权限失败，请稍后重试');
                    });
            }

            // 显示成功消息
            function showSuccess(message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                // 3秒后自动消失
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 3000);
            }
        });
    </script>
</body>
</html>
