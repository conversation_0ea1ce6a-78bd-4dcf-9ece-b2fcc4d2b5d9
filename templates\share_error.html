<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问失败 - MiaoMiao AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 3rem 2rem;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
        }
        
        .error-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-outline-secondary {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-outline-secondary:hover {
            transform: translateY(-2px);
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1.5rem 0;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="error-card">
        <i class="fas fa-exclamation-triangle error-icon"></i>
        <h1 class="error-title">访问失败</h1>
        <p class="error-message">{{ message }}</p>
        
        <div class="error-details">
            <i class="fas fa-info-circle me-2"></i>
            可能的原因：
            <ul class="text-start mt-2 mb-0">
                <li>分享链接已过期</li>
                <li>分享链接已被删除</li>
                <li>分享链接地址错误</li>
                <li>需要正确的访问秘钥</li>
            </ul>
        </div>
        
        <div class="d-grid gap-2">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>返回首页
            </a>
            <button class="btn btn-outline-secondary" onclick="history.back()">
                <i class="fas fa-arrow-left me-2"></i>返回上一页
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-question-circle me-1"></i>
                如有疑问，请联系分享者或网站管理员
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const errorCard = document.querySelector('.error-card');
            errorCard.style.opacity = '0';
            errorCard.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                errorCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                errorCard.style.opacity = '1';
                errorCard.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
